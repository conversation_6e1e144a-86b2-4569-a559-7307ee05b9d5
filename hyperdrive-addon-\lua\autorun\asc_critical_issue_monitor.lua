--[[
    Advanced Space Combat - Critical Issue Monitor v1.0.0
    
    Monitors and automatically fixes critical issues identified in console logs:
    - Memory management issues
    - Tool restoration failures  
    - CAP integration problems
    - AI system errors
    - Performance degradation
]]

print("[Advanced Space Combat] Critical Issue Monitor v1.0.0 - Loading...")

-- Initialize namespace
ASC = ASC or {}
ASC.CriticalMonitor = ASC.CriticalMonitor or {}

-- Configuration
ASC.CriticalMonitor.Config = {
    Enabled = true,
    MonitorInterval = 30, -- seconds (increased from 5 to reduce frequency)
    MemoryGrowthThreshold = 2000, -- % growth that triggers alert (increased threshold)
    PerformanceThreshold = 20, -- FPS threshold (lowered to reduce alerts)
    AutoFix = true,
    LogIssues = false, -- Disabled to reduce console spam
    SilentMode = true, -- Reduce console output

    -- Issue tracking
    MaxIssueHistory = 50, -- Reduced from 100
    AlertCooldown = 120 -- seconds between same type alerts (increased from 30)
}

-- State tracking
ASC.CriticalMonitor.State = {
    LastCheck = 0,
    IssueHistory = {},
    LastAlerts = {},
    SystemHealth = 100,
    
    -- Performance metrics
    MemoryBaseline = 0,
    FPSHistory = {},
    MemoryHistory = {},
    
    -- Issue counters
    MemoryIssues = 0,
    ToolIssues = 0,
    CAPIssues = 0,
    AIIssues = 0,
    PerformanceIssues = 0
}

-- Issue detection functions
ASC.CriticalMonitor.DetectMemoryIssues = function()
    local currentMemory = collectgarbage("count") / 1024 -- MB
    
    if ASC.CriticalMonitor.State.MemoryBaseline == 0 then
        ASC.CriticalMonitor.State.MemoryBaseline = currentMemory
        return false
    end
    
    local growth = ((currentMemory - ASC.CriticalMonitor.State.MemoryBaseline) / ASC.CriticalMonitor.State.MemoryBaseline) * 100
    
    table.insert(ASC.CriticalMonitor.State.MemoryHistory, currentMemory)
    if #ASC.CriticalMonitor.State.MemoryHistory > 60 then
        table.remove(ASC.CriticalMonitor.State.MemoryHistory, 1)
    end
    
    if growth > ASC.CriticalMonitor.Config.MemoryGrowthThreshold then
        return true, {
            type = "memory_leak",
            severity = "critical",
            growth = growth,
            current = currentMemory,
            baseline = ASC.CriticalMonitor.State.MemoryBaseline
        }
    end
    
    return false
end

ASC.CriticalMonitor.DetectToolIssues = function()
    if ASC.ToolRestoration and ASC.ToolRestoration.State then
        -- Check for missing RequiredTools table
        if not ASC.ToolRestoration.RequiredTools or type(ASC.ToolRestoration.RequiredTools) ~= "table" then
            return true, {
                type = "tool_restoration_missing_table",
                severity = "critical",
                message = "RequiredTools table is missing or invalid"
            }
        end

        -- Check for invalid MissingTools table
        if ASC.ToolRestoration.State.MissingTools and type(ASC.ToolRestoration.State.MissingTools) ~= "table" then
            return true, {
                type = "tool_restoration_invalid_state",
                severity = "high",
                message = "MissingTools state is not a valid table"
            }
        end

        local retryCount = ASC.ToolRestoration.State.RetryCount or 0
        local missingTools = #(ASC.ToolRestoration.State.MissingTools or {})

        if retryCount >= 3 and missingTools > 0 then
            return true, {
                type = "tool_restoration_failure",
                severity = "high",
                retryCount = retryCount,
                missingTools = missingTools
            }
        end
    end

    return false
end

ASC.CriticalMonitor.DetectCAPIssues = function()
    -- Check for CAP fallback system namespace corruption
    if not ASC or not ASC.CAP or not ASC.CAP.Fallback or type(ASC.CAP.Fallback) ~= "table" then
        return true, {
            type = "cap_fallback_namespace_corruption",
            severity = "high",
            message = "CAP fallback namespace corrupted"
        }
    end

    -- Check for missing CleanupVirtualEntities function
    if not ASC.CAP.Fallback.CleanupVirtualEntities or type(ASC.CAP.Fallback.CleanupVirtualEntities) ~= "function" then
        return true, {
            type = "cap_fallback_function_missing",
            severity = "high",
            message = "CleanupVirtualEntities function missing"
        }
    end

    if HYPERDRIVE and HYPERDRIVE.CAP and HYPERDRIVE.CAP.Detection then
        local detection = HYPERDRIVE.CAP.Detection
        local componentCount = detection.workshopComponents or 0

        if componentCount < 10 and detection.fallbackMode then
            return true, {
                type = "cap_integration_failure",
                severity = "medium",
                componentCount = componentCount,
                fallbackMode = detection.fallbackMode
            }
        end
    end

    return false
end

ASC.CriticalMonitor.DetectAIIssues = function()
    -- Check for AI system initialization
    if ASC.AI and not ASC.AI.NLP then
        return true, {
            type = "ai_nlp_missing",
            severity = "high",
            message = "NLP system not initialized"
        }
    end

    -- Check for AI Languages Database missing
    if ASC.AI and ASC.AI.Languages and not ASC.AI.Languages.Database then
        return true, {
            type = "ai_languages_database_missing",
            severity = "high",
            message = "AI Languages Database not initialized"
        }
    end

    -- Check for missing SetLanguage function
    if ASC.AI and ASC.AI.Languages and not ASC.AI.Languages.SetLanguage then
        return true, {
            type = "ai_setlanguage_function_missing",
            severity = "high",
            message = "AI SetLanguage function not available"
        }
    end

    -- Check for neural network issues
    if ASC.AI and ASC.AI.NeuralNetwork and not ASC.AI.NeuralNetwork.Initialized then
        return true, {
            type = "ai_neural_network_failure",
            severity = "medium",
            message = "Neural network not initialized"
        }
    end

    return false
end

ASC.CriticalMonitor.DetectPerformanceIssues = function()
    local currentFPS = 1 / FrameTime()
    
    table.insert(ASC.CriticalMonitor.State.FPSHistory, currentFPS)
    if #ASC.CriticalMonitor.State.FPSHistory > 60 then
        table.remove(ASC.CriticalMonitor.State.FPSHistory, 1)
    end
    
    if currentFPS < ASC.CriticalMonitor.Config.PerformanceThreshold then
        -- Calculate average FPS over last 10 seconds
        local recentFPS = {}
        for i = math.max(1, #ASC.CriticalMonitor.State.FPSHistory - 10), #ASC.CriticalMonitor.State.FPSHistory do
            table.insert(recentFPS, ASC.CriticalMonitor.State.FPSHistory[i])
        end
        
        local avgFPS = 0
        for _, fps in ipairs(recentFPS) do
            avgFPS = avgFPS + fps
        end
        avgFPS = avgFPS / #recentFPS
        
        if avgFPS < ASC.CriticalMonitor.Config.PerformanceThreshold then
            return true, {
                type = "performance_degradation",
                severity = "medium",
                currentFPS = currentFPS,
                averageFPS = avgFPS,
                threshold = ASC.CriticalMonitor.Config.PerformanceThreshold
            }
        end
    end
    
    return false
end

-- Auto-fix functions
ASC.CriticalMonitor.FixMemoryIssue = function(issue)
    print("[Critical Monitor] Fixing memory issue: " .. issue.growth .. "% growth detected")
    
    -- Trigger emergency cleanup
    if ASC.MemoryOptimizer and ASC.MemoryOptimizer.EmergencyCleanup then
        ASC.MemoryOptimizer.EmergencyCleanup()
    end
    
    -- Force aggressive garbage collection
    for i = 1, 5 do
        collectgarbage("collect")
    end
    
    -- Reset baseline
    ASC.CriticalMonitor.State.MemoryBaseline = collectgarbage("count") / 1024
    
    return true
end

ASC.CriticalMonitor.FixToolIssue = function(issue)
    print("[Critical Monitor] Fixing tool issue: " .. issue.message)

    if issue.type == "tool_restoration_missing_table" then
        -- Restore RequiredTools table
        if ASC.ToolRestoration then
            ASC.ToolRestoration.RequiredTools = {
                {
                    class = "asc_ship_core_tool",
                    name = "ASC Ship Core Tool",
                    category = "Advanced Space Combat",
                    file = "lua/weapons/gmod_tool/stools/asc_ship_core_tool.lua"
                },
                {
                    class = "asc_main_tool",
                    name = "ASC Main Tool",
                    category = "Advanced Space Combat",
                    file = "lua/weapons/gmod_tool/stools/asc_main_tool.lua"
                },
                {
                    class = "asc_hyperdrive_tool",
                    name = "ASC Hyperdrive Tool",
                    category = "Advanced Space Combat",
                    file = "lua/weapons/gmod_tool/stools/asc_hyperdrive_tool.lua"
                }
            }
            print("[Critical Monitor] Restored RequiredTools table with essential tools")
        end

    elseif issue.type == "tool_restoration_invalid_state" then
        -- Fix invalid MissingTools state
        if ASC.ToolRestoration and ASC.ToolRestoration.State then
            ASC.ToolRestoration.State.MissingTools = {}
            ASC.ToolRestoration.State.RetryCount = 0
            print("[Critical Monitor] Reset tool restoration state")
        end

    elseif issue.type == "tool_restoration_failure" then
        print("[Critical Monitor] Fixing tool restoration failure: " .. issue.missingTools .. " tools missing")

        if ASC.ToolRestoration and ASC.ToolRestoration.ManualRestore then
            ASC.ToolRestoration.ManualRestore()
        end
    end

    return true
end

ASC.CriticalMonitor.FixCAPIssue = function(issue)
    print("[Critical Monitor] Fixing CAP issue: " .. issue.message)

    if issue.type == "cap_fallback_namespace_corruption" then
        -- Restore CAP fallback namespace
        ASC = ASC or {}
        ASC.CAP = ASC.CAP or {}
        ASC.CAP.Fallback = ASC.CAP.Fallback or {}

        -- Reload the fallback system
        if include then
            local success, err = pcall(include, "autorun/asc_cap_fallback_system.lua")
            if success then
                print("[Critical Monitor] CAP fallback system reloaded successfully")
            else
                print("[Critical Monitor] Failed to reload CAP fallback system: " .. tostring(err))
            end
        end

    elseif issue.type == "cap_fallback_function_missing" then
        -- Try to restore the missing function
        if ASC.CAP and ASC.CAP.Fallback then
            ASC.CAP.Fallback.CleanupVirtualEntities = function()
                local currentTime = CurTime()
                local lifetime = 300 -- 5 minutes default

                if not ASC.CAP.Fallback.VirtualEntities then
                    ASC.CAP.Fallback.VirtualEntities = {}
                    return
                end

                for i = #ASC.CAP.Fallback.VirtualEntities, 1, -1 do
                    local entity = ASC.CAP.Fallback.VirtualEntities[i]
                    if entity and entity.created and currentTime - entity.created > lifetime then
                        table.remove(ASC.CAP.Fallback.VirtualEntities, i)
                    end
                end
            end
            print("[Critical Monitor] Restored CleanupVirtualEntities function")
        end

    elseif issue.type == "cap_integration_failure" then
        print("[Critical Monitor] Fixing CAP integration issue: " .. issue.componentCount .. " components detected")

        -- Force CAP re-detection
        if HYPERDRIVE and HYPERDRIVE.CAP and HYPERDRIVE.CAP.DetectCAP then
            HYPERDRIVE.CAP.DetectCAP()
        end

        -- Initialize fallback systems
        if ASC.CAP and ASC.CAP.Fallback and ASC.CAP.Fallback.Initialize then
            ASC.CAP.Fallback.Initialize()
        end
    end

    return true
end

ASC.CriticalMonitor.FixAIIssue = function(issue)
    print("[Critical Monitor] Fixing AI issue: " .. issue.message)
    
    -- Reinitialize NLP system
    if issue.type == "ai_nlp_missing" and ASC.AI then
        ASC.AI.NLP = ASC.AI.NLP or {}

        -- Ensure basic NLP components exist
        if not ASC.AI.NLP.IntentPatterns then
            ASC.AI.NLP.IntentPatterns = {
                question = {
                    patterns = {{words = {"what", "how", "why", "when", "where"}, weight = 1.0}},
                    confidence_threshold = 0.6
                }
            }
        end

        if not ASC.AI.NLP.SentimentWords then
            ASC.AI.NLP.SentimentWords = {
                positive = {"good", "great", "excellent", "awesome"},
                negative = {"bad", "terrible", "awful", "broken"},
                neutral = {"okay", "fine", "normal"}
            }
        end

        if not ASC.AI.NLP.ContextKeywords then
            ASC.AI.NLP.ContextKeywords = {
                ship = {"ship", "vessel", "craft"},
                weapon = {"weapon", "gun", "cannon"},
                stargate = {"stargate", "gate", "portal"}
            }
        end

    elseif issue.type == "ai_languages_database_missing" and ASC.AI and ASC.AI.Languages then
        -- Restore AI Languages Database
        ASC.AI.Languages.Database = {
            english = {
                greeting = "Hello! I'm ARIA-4, your Advanced Space Combat AI assistant.",
                help_prompt = "How can I help you today?",
                error_message = "I'm sorry, I didn't understand that. Could you rephrase?",
                goodbye = "Goodbye! Feel free to ask me anything anytime.",
                loading = "Processing your request...",
                success = "Task completed successfully!",
                warning = "Warning: Please check your configuration.",
                tip = "Tip:",
                suggestion = "Suggestion:"
            },
            czech = {
                greeting = "Ahoj! Jsem ARIA-4, váš pokročilý AI asistent pro vesmírný boj.",
                help_prompt = "Jak vám mohu dnes pomoci?",
                error_message = "Promiňte, nerozuměl jsem. Můžete to přeformulovat?",
                goodbye = "Na shledanou! Neváhejte se mě zeptat na cokoliv.",
                loading = "Zpracovávám váš požadavek...",
                success = "Úkol úspěšně dokončen!",
                warning = "Varování: Zkontrolujte prosím svou konfiguraci.",
                tip = "Tip:",
                suggestion = "Návrh:"
            }
        }
        print("[Critical Monitor] Restored AI Languages Database with English and Czech")

    elseif issue.type == "ai_setlanguage_function_missing" and ASC.AI and ASC.AI.Languages then
        -- Restore SetLanguage function
        ASC.AI.Languages.SetLanguage = function(playerID, language)
            if not ASC.AI.UserProfiles then
                ASC.AI.UserProfiles = {}
            end

            if not ASC.AI.UserProfiles[playerID] then
                ASC.AI.UserProfiles[playerID] = {}
            end

            -- Convert language code to full name if needed
            local fullLanguageName = language
            local languageMapping = {
                cs = "czech",
                en = "english",
                es = "spanish",
                fr = "french"
            }

            if languageMapping[language] then
                fullLanguageName = languageMapping[language]
            end

            if ASC.AI.Languages.Database and ASC.AI.Languages.Database[fullLanguageName] then
                ASC.AI.UserProfiles[playerID].language_preference = fullLanguageName
                return true
            end

            return false
        end
        print("[Critical Monitor] Restored AI SetLanguage function")
    end
    
    -- Reinitialize neural network
    if issue.type == "ai_neural_network_failure" and ASC.AI and ASC.AI.NeuralNetwork then
        if ASC.AI.NeuralNetwork.Initialize then
            ASC.AI.NeuralNetwork.Initialize()
        end
    end
    
    return true
end

ASC.CriticalMonitor.FixPerformanceIssue = function(issue)
    print("[Critical Monitor] Fixing performance issue: " .. string.format("%.1f", issue.averageFPS) .. " FPS")
    
    -- Reduce quality settings
    if ASC.Performance and ASC.Performance.ReduceQuality then
        ASC.Performance.ReduceQuality()
    end
    
    -- Trigger optimization
    if ASC.MasterScheduler and ASC.MasterScheduler.OptimizePerformance then
        ASC.MasterScheduler.OptimizePerformance()
    end
    
    return true
end

-- Main monitoring function
ASC.CriticalMonitor.RunCheck = function()
    if not ASC.CriticalMonitor.Config.Enabled then return end
    
    local currentTime = CurTime()
    local issues = {}
    
    -- Detect all issue types
    local memoryIssue, memoryData = ASC.CriticalMonitor.DetectMemoryIssues()
    if memoryIssue then
        table.insert(issues, memoryData)
        ASC.CriticalMonitor.State.MemoryIssues = ASC.CriticalMonitor.State.MemoryIssues + 1
    end
    
    local toolIssue, toolData = ASC.CriticalMonitor.DetectToolIssues()
    if toolIssue then
        table.insert(issues, toolData)
        ASC.CriticalMonitor.State.ToolIssues = ASC.CriticalMonitor.State.ToolIssues + 1
    end
    
    local capIssue, capData = ASC.CriticalMonitor.DetectCAPIssues()
    if capIssue then
        table.insert(issues, capData)
        ASC.CriticalMonitor.State.CAPIssues = ASC.CriticalMonitor.State.CAPIssues + 1
    end
    
    local aiIssue, aiData = ASC.CriticalMonitor.DetectAIIssues()
    if aiIssue then
        table.insert(issues, aiData)
        ASC.CriticalMonitor.State.AIIssues = ASC.CriticalMonitor.State.AIIssues + 1
    end
    
    local perfIssue, perfData = ASC.CriticalMonitor.DetectPerformanceIssues()
    if perfIssue then
        table.insert(issues, perfData)
        ASC.CriticalMonitor.State.PerformanceIssues = ASC.CriticalMonitor.State.PerformanceIssues + 1
    end
    
    -- Process and fix issues
    for _, issue in ipairs(issues) do
        local lastAlert = ASC.CriticalMonitor.State.LastAlerts[issue.type] or 0
        
        if currentTime - lastAlert > ASC.CriticalMonitor.Config.AlertCooldown then
            print("[Critical Monitor] Issue detected: " .. issue.type .. " (severity: " .. issue.severity .. ")")
            
            if ASC.CriticalMonitor.Config.AutoFix then
                local fixed = false
                
                if issue.type == "memory_leak" then
                    fixed = ASC.CriticalMonitor.FixMemoryIssue(issue)
                elseif issue.type == "tool_restoration_failure" then
                    fixed = ASC.CriticalMonitor.FixToolIssue(issue)
                elseif issue.type == "cap_integration_failure" then
                    fixed = ASC.CriticalMonitor.FixCAPIssue(issue)
                elseif string.find(issue.type, "ai_") then
                    fixed = ASC.CriticalMonitor.FixAIIssue(issue)
                elseif issue.type == "performance_degradation" then
                    fixed = ASC.CriticalMonitor.FixPerformanceIssue(issue)
                end
                
                if fixed then
                    print("[Critical Monitor] Issue fixed: " .. issue.type)
                else
                    print("[Critical Monitor] Failed to fix issue: " .. issue.type)
                end
            end
            
            ASC.CriticalMonitor.State.LastAlerts[issue.type] = currentTime
        end
        
        -- Store in history
        table.insert(ASC.CriticalMonitor.State.IssueHistory, {
            issue = issue,
            timestamp = currentTime
        })
        
        if #ASC.CriticalMonitor.State.IssueHistory > ASC.CriticalMonitor.Config.MaxIssueHistory then
            table.remove(ASC.CriticalMonitor.State.IssueHistory, 1)
        end
    end
    
    ASC.CriticalMonitor.State.LastCheck = currentTime
end

-- Initialize monitoring
ASC.CriticalMonitor.Initialize = function()
    print("[Critical Monitor] Initializing critical issue monitoring...")
    
    -- Set initial memory baseline
    ASC.CriticalMonitor.State.MemoryBaseline = collectgarbage("count") / 1024
    
    -- Start monitoring timer
    timer.Create("ASC_CriticalMonitor", ASC.CriticalMonitor.Config.MonitorInterval, 0, function()
        ASC.CriticalMonitor.RunCheck()
    end)
    
    print("[Critical Monitor] Critical issue monitoring initialized")
end

-- Console command for manual check
concommand.Add("asc_check_critical_issues", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then return end
    
    print("[Critical Monitor] Running manual critical issue check...")
    ASC.CriticalMonitor.RunCheck()
    
    local state = ASC.CriticalMonitor.State
    print("[Critical Monitor] Issue Summary:")
    print("  Memory Issues: " .. state.MemoryIssues)
    print("  Tool Issues: " .. state.ToolIssues)
    print("  CAP Issues: " .. state.CAPIssues)
    print("  AI Issues: " .. state.AIIssues)
    print("  Performance Issues: " .. state.PerformanceIssues)
    print("  Current Memory: " .. string.format("%.2f", collectgarbage("count") / 1024) .. "MB")
    print("  Current FPS: " .. string.format("%.1f", 1 / FrameTime()))
end)

-- Initialize after a delay
timer.Simple(5, function()
    ASC.CriticalMonitor.Initialize()
end)

print("[Advanced Space Combat] Critical Issue Monitor v1.0.0 loaded successfully!")
