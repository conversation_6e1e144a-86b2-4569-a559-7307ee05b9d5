-- Ship Core Entity - Shared v4.0.0 - ARIA-4 Ultimate Edition
-- Mandatory ship core for Advanced Space Combat with ARIA-4 features and ultimate engine support

ENT.Type = "anim"
ENT.Base = "base_gmodentity"

-- Use localization for entity properties
ENT.PrintName = ASC_GetText("asc.entity.asc_ship_core.name", "ASC Ship Core")
ENT.Author = "Advanced Space Combat Team"
ENT.Contact = ""
ENT.Purpose = ASC_GetText("asc.entity.asc_ship_core.purpose", "Central command and control system for ships with ARIA-4 AI integration")
ENT.Instructions = ASC_GetText("asc.entity.asc_ship_core.instructions", "Mandatory ship core for hyperdrive operation. Auto-links to nearby components. Use E to access interface. Compatible with ultimate engine system.")

ENT.Spawnable = true
ENT.AdminSpawnable = true
ENT.Category = ASC_GetText("asc.category.ship_cores", "Advanced Space Combat - Ship Cores")

ENT.RenderGroup = RENDERGROUP_OPAQUE

-- Ship core states
ENT.States = {
    INACTIVE = 0,
    ACTIVE = 1,
    INVALID = 2,
    CRITICAL = 3,
    EMERGENCY = 4
}

-- Enhanced Network variables following <PERSON>'s Mod best practices
function ENT:SetupDataTables()
    -- Core system variables (Int 0-31 supported)
    self:NetworkVar("Int", 0, "State")
    self:NetworkVar("Int", 1, "HullIntegrity")
    self:NetworkVar("Int", 2, "ShieldStrength")
    self:NetworkVar("Int", 3, "EntityCount")
    self:NetworkVar("Int", 4, "PlayerCount")
    self:NetworkVar("Int", 5, "ShipMass")

    -- Boolean variables (Bool 0-31 supported)
    self:NetworkVar("Bool", 0, "ShipDetected")
    self:NetworkVar("Bool", 1, "CoreValid")
    self:NetworkVar("Bool", 2, "HullSystemActive")
    self:NetworkVar("Bool", 3, "ShieldSystemActive")
    self:NetworkVar("Bool", 4, "ResourceSystemActive")
    self:NetworkVar("Bool", 5, "LifeSupportActive")
    self:NetworkVar("Bool", 6, "AutoProvisionEnabled")
    self:NetworkVar("Bool", 7, "WeldDetectionEnabled")

    -- String variables (String 0-3 supported - limited!)
    self:NetworkVar("String", 0, "ShipType")
    self:NetworkVar("String", 1, "StatusMessage")
    self:NetworkVar("String", 2, "ShipName")
    self:NetworkVar("String", 3, "LastResourceActivity")

    -- Vector variables (Vector 0-31 supported)
    self:NetworkVar("Vector", 0, "ShipCenter")
    self:NetworkVar("Vector", 1, "FrontDirection")

    -- Float variables for resource data (Float 0-31 supported)
    self:NetworkVar("Float", 0, "EnergyLevel")
    self:NetworkVar("Float", 1, "OxygenLevel")
    self:NetworkVar("Float", 2, "CoolantLevel")
    self:NetworkVar("Float", 3, "FuelLevel")
    self:NetworkVar("Float", 4, "WaterLevel")
    self:NetworkVar("Float", 5, "NitrogenLevel")
    self:NetworkVar("Float", 6, "EnergyCapacity")
    self:NetworkVar("Float", 7, "OxygenCapacity")
    self:NetworkVar("Float", 8, "CoolantCapacity")
    self:NetworkVar("Float", 9, "FuelCapacity")
    self:NetworkVar("Float", 10, "WaterCapacity")
    self:NetworkVar("Float", 11, "NitrogenCapacity")
    self:NetworkVar("Float", 12, "EnergyPercent")
    self:NetworkVar("Float", 13, "OxygenPercent")
    self:NetworkVar("Float", 14, "FuelPercent")
    self:NetworkVar("Float", 15, "TotalResourceAmount")
    self:NetworkVar("Float", 16, "TotalResourceCapacity")
    self:NetworkVar("Float", 17, "ResourceDistributionRate")
    self:NetworkVar("Float", 18, "ResourceCollectionRate")
    self:NetworkVar("Float", 19, "ShipSizeMultiplier")

    -- Initialize default values on server only
    if SERVER then
        self:SetState(self.States.INACTIVE)
        self:SetHullIntegrity(100)
        self:SetShieldStrength(0)
        self:SetShipDetected(false)
        self:SetCoreValid(true)
        self:SetHullSystemActive(false)
        self:SetShieldSystemActive(false)
        self:SetResourceSystemActive(false)
        self:SetLifeSupportActive(false)
        self:SetAutoProvisionEnabled(false)
        self:SetWeldDetectionEnabled(false)
        self:SetShipType("Unknown")
        self:SetStatusMessage("Initializing...")
        self:SetShipName("Unnamed Ship")
        self:SetLastResourceActivity("Initializing")
        self:SetShipCenter(Vector(0, 0, 0))
        self:SetFrontDirection(Vector(1, 0, 0))
        self:SetEntityCount(0)
        self:SetPlayerCount(0)
        self:SetShipMass(0)

        -- Initialize resource values
        self:SetEnergyLevel(0)
        self:SetOxygenLevel(0)
        self:SetCoolantLevel(0)
        self:SetFuelLevel(0)
        self:SetWaterLevel(0)
        self:SetNitrogenLevel(0)
        self:SetEnergyCapacity(0)
        self:SetOxygenCapacity(0)
        self:SetCoolantCapacity(0)
        self:SetFuelCapacity(0)
        self:SetWaterCapacity(0)
        self:SetNitrogenCapacity(0)
        self:SetEnergyPercent(0)
        self:SetOxygenPercent(0)
        self:SetFuelPercent(0)
        self:SetTotalResourceAmount(0)
        self:SetTotalResourceCapacity(0)
        self:SetResourceDistributionRate(0)
        self:SetResourceCollectionRate(0)
        self:SetShipSizeMultiplier(1.0)
    end
end

-- Get state color for UI
function ENT:GetStateColor()
    local state = self:GetState()

    if state == self.States.EMERGENCY then
        return Color(255, 50, 50) -- Red
    elseif state == self.States.CRITICAL then
        return Color(255, 150, 50) -- Orange
    elseif state == self.States.INVALID then
        return Color(255, 255, 50) -- Yellow
    elseif state == self.States.ACTIVE then
        return Color(50, 255, 50) -- Green
    else
        return Color(100, 100, 100) -- Gray
    end
end

-- Get state name
function ENT:GetStateName()
    local state = self:GetState()

    if state == self.States.EMERGENCY then
        return "EMERGENCY"
    elseif state == self.States.CRITICAL then
        return "CRITICAL"
    elseif state == self.States.INVALID then
        return "INVALID"
    elseif state == self.States.ACTIVE then
        return "ACTIVE"
    else
        return "INACTIVE"
    end
end

-- Get hull status color
function ENT:GetHullColor()
    local integrity = self:GetHullIntegrity()

    if integrity < 25 then
        return Color(255, 50, 50) -- Red
    elseif integrity < 50 then
        return Color(255, 150, 50) -- Orange
    elseif integrity < 75 then
        return Color(255, 255, 50) -- Yellow
    else
        return Color(50, 255, 50) -- Green
    end
end

-- Get shield status color
function ENT:GetShieldColor()
    local strength = self:GetShieldStrength()

    if strength < 25 then
        return Color(255, 50, 50) -- Red
    elseif strength < 50 then
        return Color(255, 150, 50) -- Orange
    elseif strength < 75 then
        return Color(255, 255, 50) -- Yellow
    else
        return Color(50, 255, 50) -- Green
    end
end

-- Wire inputs and outputs
if WireLib then
    ENT.Inputs = {
        -- Resource management inputs
        {"DistributeResources", "NORMAL"},
        {"CollectResources", "NORMAL"},
        {"BalanceResources", "NORMAL"},
        {"AddEnergy", "NORMAL"},
        {"AddOxygen", "NORMAL"},
        {"AddCoolant", "NORMAL"},
        {"AddFuel", "NORMAL"},
        {"AddWater", "NORMAL"},
        {"AddNitrogen", "NORMAL"},
        {"SetEnergyCapacity", "NORMAL"},
        {"SetOxygenCapacity", "NORMAL"},
        {"SetCoolantCapacity", "NORMAL"},
        {"SetFuelCapacity", "NORMAL"},
        {"SetWaterCapacity", "NORMAL"},
        {"SetNitrogenCapacity", "NORMAL"},
        {"ToggleAutoProvision", "NORMAL"},
        {"ToggleWeldDetection", "NORMAL"},
        {"ToggleLifeSupport", "NORMAL"},
        {"EmergencyResourceShutdown", "NORMAL"},

        -- System control inputs (Mute removed per user request)
        {"RepairHull", "NORMAL"},
        {"ActivateShields", "NORMAL"},
        {"DeactivateShields", "NORMAL"},
        {"Recalculate", "NORMAL"}
    }

    ENT.Outputs = {
        {"ShipDetected", "NORMAL"},
        {"ShipType", "STRING"},
        {"ShipName", "STRING"},
        {"EntityCount", "NORMAL"},
        {"PlayerCount", "NORMAL"},
        {"ShipMass", "NORMAL"},
        {"CoreValid", "NORMAL"},
        {"CoreState", "NORMAL"},
        {"CoreStateName", "STRING"},
        {"StatusMessage", "STRING"},
        {"HullIntegrity", "NORMAL"},
        {"HullSystemActive", "NORMAL"},
        {"ShieldStrength", "NORMAL"},
        {"ShieldSystemActive", "NORMAL"},
        {"ShipCenter", "VECTOR"},
        {"FrontDirection", "VECTOR"},

        -- Resource system outputs
        {"EnergyLevel", "NORMAL"},
        {"OxygenLevel", "NORMAL"},
        {"CoolantLevel", "NORMAL"},
        {"FuelLevel", "NORMAL"},
        {"WaterLevel", "NORMAL"},
        {"NitrogenLevel", "NORMAL"},

        -- Resource capacity outputs
        {"EnergyCapacity", "NORMAL"},
        {"OxygenCapacity", "NORMAL"},
        {"CoolantCapacity", "NORMAL"},
        {"FuelCapacity", "NORMAL"},
        {"WaterCapacity", "NORMAL"},
        {"NitrogenCapacity", "NORMAL"},

        -- Resource percentage outputs
        {"EnergyPercent", "NORMAL"},
        {"OxygenPercent", "NORMAL"},
        {"CoolantPercent", "NORMAL"},
        {"FuelPercent", "NORMAL"},
        {"WaterPercent", "NORMAL"},
        {"NitrogenPercent", "NORMAL"},

        -- Resource system status
        {"ResourceEmergency", "NORMAL"},
        {"ResourceSystemActive", "NORMAL"},
        {"LifeSupportActive", "NORMAL"},
        {"TotalResourceCapacity", "NORMAL"},
        {"TotalResourceAmount", "NORMAL"},
        {"AutoProvisionEnabled", "NORMAL"},
        {"WeldDetectionEnabled", "NORMAL"},
        {"LastResourceActivity", "STRING"},
        {"ResourceDistributionRate", "NORMAL"},
        {"ResourceCollectionRate", "NORMAL"},
        {"PlayersSupported", "NORMAL"},
        {"ShipSizeMultiplier", "NORMAL"},

        -- Ambient sound outputs removed per user request
    }
end
