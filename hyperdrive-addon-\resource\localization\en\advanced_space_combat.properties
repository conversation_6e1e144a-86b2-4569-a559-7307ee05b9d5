# Advanced Space Combat - English Localization
# Official <PERSON>'s Mod localization file for Advanced Space Combat addon
# All user-facing text strings for the addon

# === CORE SYSTEM MESSAGES ===
asc.addon.name=Advanced Space Combat
asc.addon.version=Version
asc.addon.build=Build
asc.addon.loading=Loading...
asc.addon.initializing=Initializing
asc.addon.ready=Ready
asc.addon.error=Error
asc.addon.warning=Warning
asc.addon.info=Information
asc.addon.success=Success
asc.addon.failed=Failed

# === SYSTEM STATUS ===
asc.status.enabled=Enabled
asc.status.disabled=Disabled
asc.status.online=Online
asc.status.offline=Offline
asc.status.active=Active
asc.status.inactive=Inactive
asc.status.ready=Ready
asc.status.not_ready=Not Ready
asc.status.system_status=System Status

# === SHIP CORE SYSTEM ===
asc.ship_core.name=Ship Core
asc.ship_core.description=Central ship management and control system
asc.ship_core.status=Ship Core Status
asc.ship_core.health=Hull Integrity
asc.ship_core.energy=Energy Level
asc.ship_core.shields=Shield Status
asc.ship_core.life_support=Life Support
asc.ship_core.orientation=Ship Orientation
asc.ship_core.detected=Ship Core Detected
asc.ship_core.not_detected=No Ship Core Detected
asc.ship_core.multiple_detected=Multiple Ship Cores Detected
asc.ship_core.name_set=Ship Name Set
asc.ship_core.name_prompt=Enter ship name:
asc.ship_core.unknown=Unknown

# === HYPERDRIVE SYSTEM ===
asc.hyperdrive.name=Hyperdrive Engine
asc.hyperdrive.description=Advanced FTL propulsion system
asc.hyperdrive.status=Hyperdrive Status
asc.hyperdrive.charging=Charging
asc.hyperdrive.ready=Ready for Jump
asc.hyperdrive.jumping=Jumping
asc.hyperdrive.cooldown=Cooldown
asc.hyperdrive.destination=Destination
asc.hyperdrive.coordinates=Coordinates
asc.hyperdrive.jump_initiated=Hyperspace jump initiated
asc.hyperdrive.jump_complete=Hyperspace jump complete
asc.hyperdrive.jump_aborted=Hyperspace jump aborted
asc.hyperdrive.no_destination=No destination set
asc.hyperdrive.invalid_destination=Invalid destination
asc.hyperdrive.insufficient_energy=Insufficient energy

# === WEAPONS SYSTEM ===
asc.weapons.name=Weapons System
asc.weapons.status=Weapons Status
asc.weapons.online=Weapons Online
asc.weapons.offline=Weapons Offline
asc.weapons.charging=Charging
asc.weapons.ready=Ready to Fire
asc.weapons.firing=Firing
asc.weapons.reloading=Reloading
asc.weapons.ammunition=Ammunition
asc.weapons.target_acquired=Target Acquired
asc.weapons.target_lost=Target Lost
asc.weapons.no_target=No Target
asc.weapons.out_of_ammo=Out of Ammunition

# === SHIELD SYSTEM ===
asc.shields.name=Shield System
asc.shields.status=Shield Status
asc.shields.active=Shields Active
asc.shields.inactive=Shields Inactive
asc.shields.charging=Shields Charging
asc.shields.depleted=Shields Depleted
asc.shields.strength=Shield Strength
asc.shields.regenerating=Regenerating
asc.shields.overloaded=Shields Overloaded
asc.shields.radius=Shield Radius
asc.shields.auto_link=Auto-link to ship core
asc.shields.invalid_type=Invalid shield type!
asc.shields.creation_failed=Failed to create shield entity!
asc.shields.spawned_successfully=Shield spawned successfully!
asc.shields.linked_to_core=Shield linked to ship core
asc.shields.no_core_found=No ship core found for linking
asc.shields.configure_prompt=Right click on a shield to configure it!
asc.shields.toggled=Shield toggled!
asc.shields.instructions.left_click=Left click: Spawn shield generator
asc.shields.instructions.right_click=Right click: Configure shield
asc.shields.instructions.reload=Reload: Toggle shield on/off
asc.shields.config_menu=Shield configuration menu would open here!

# === FLIGHT SYSTEM ===
asc.flight.name=Flight System
asc.flight.status=Flight Status
asc.flight.engaged=Flight Mode Engaged
asc.flight.disengaged=Flight Mode Disengaged
asc.flight.autopilot=Autopilot
asc.flight.manual=Manual Control
asc.flight.stabilizing=Stabilizing
asc.flight.altitude=Altitude
asc.flight.velocity=Velocity
asc.flight.heading=Heading

# === AI SYSTEM ===
asc.ai.name=ARIA-4 AI
asc.ai.status=AI Status
asc.ai.online=AI Online
asc.ai.offline=AI Offline
asc.ai.processing=Processing
asc.ai.ready=AI Ready
asc.ai.greeting=Hello! I'm ARIA-4, your Advanced Space Combat AI assistant. How can I help you?
asc.ai.help=Type 'aria help' for available commands
asc.ai.command_not_found=Command not recognized
asc.ai.invalid_syntax=Invalid command syntax
asc.ai.access_denied=Access denied

# === CONSOLE COMMANDS ===
asc.commands.help=Help
asc.commands.status=Status
asc.commands.spawn=Spawn
asc.commands.remove=Remove
asc.commands.configure=Configure
asc.commands.reset=Reset
asc.commands.debug=Debug
asc.commands.admin=Admin
asc.commands.general=General
asc.commands.ship_management=Ship Management
asc.commands.combat=Combat
asc.commands.flight=Flight
asc.commands.transport=Transport
asc.commands.configuration=Configuration

# === NOTIFICATIONS ===
asc.notification.welcome=Welcome to Advanced Space Combat!
asc.notification.system_ready=All systems ready
asc.notification.error_occurred=An error occurred
asc.notification.command_executed=Command executed successfully
asc.notification.permission_denied=Permission denied
asc.notification.feature_disabled=Feature is disabled
asc.notification.feature_enabled=Feature is enabled

# === UI ELEMENTS ===
asc.ui.close=Close
asc.ui.cancel=Cancel
asc.ui.confirm=Confirm
asc.ui.apply=Apply
asc.ui.reset=Reset
asc.ui.save=Save
asc.ui.load=Load
asc.ui.delete=Delete
asc.ui.edit=Edit
asc.ui.create=Create
asc.ui.settings=Settings
asc.ui.options=Options
asc.ui.preferences=Preferences

# === CATEGORIES ===
asc.category.ship_components=Ship Components
asc.category.weapons=Weapons
asc.category.tools=Tools
asc.category.effects=Effects
asc.category.utilities=Utilities
asc.category.admin=Admin Tools

# === UNITS ===
asc.units.meters=meters
asc.units.kilometers=kilometers
asc.units.seconds=seconds
asc.units.minutes=minutes
asc.units.hours=hours
asc.units.percent=percent
asc.units.energy=energy units
asc.units.health=health points

# === ERRORS ===
asc.error.file_not_found=File not found
asc.error.invalid_input=Invalid input
asc.error.network_error=Network error
asc.error.permission_denied=Permission denied
asc.error.system_error=System error
asc.error.unknown_error=Unknown error

# === SUCCESS MESSAGES ===
asc.success.operation_complete=Operation completed successfully
asc.success.file_saved=File saved successfully
asc.success.settings_applied=Settings applied successfully
asc.success.system_initialized=System initialized successfully

# === STARGATE INTEGRATION ===
asc.stargate.name=Stargate
asc.stargate.dialing=Dialing
asc.stargate.connected=Connected
asc.stargate.disconnected=Disconnected
asc.stargate.address=Address
asc.stargate.dial_command=Dial
asc.stargate.close_command=Close
asc.stargate.invalid_address=Invalid stargate address
asc.stargate.connection_failed=Connection failed
asc.stargate.connection_established=Connection established

# === TACTICAL AI ===
asc.tactical.name=Tactical AI
asc.tactical.status=Tactical Status
asc.tactical.analyzing=Analyzing
asc.tactical.recommending=Recommending
asc.tactical.threat_detected=Threat Detected
asc.tactical.all_clear=All Clear
asc.tactical.formation=Formation
asc.tactical.engagement=Engagement
asc.tactical.retreat=Retreat

# === DOCKING SYSTEM ===
asc.docking.name=Docking System
asc.docking.status=Docking Status
asc.docking.available=Docking Available
asc.docking.occupied=Docking Bay Occupied
asc.docking.approaching=Approaching Dock
asc.docking.docked=Docked
asc.docking.undocking=Undocking
asc.docking.permission_granted=Docking Permission Granted
asc.docking.permission_denied=Docking Permission Denied

# === FORMATION SYSTEM ===
asc.formation.name=Formation System
asc.formation.status=Formation Status
asc.formation.leader=Formation Leader
asc.formation.member=Formation Member
asc.formation.joining=Joining Formation
asc.formation.leaving=Leaving Formation
asc.formation.maintaining=Maintaining Formation
asc.formation.broken=Formation Broken

# === BOSS SYSTEM ===
asc.boss.name=Boss Encounter
asc.boss.spawning=Boss Spawning
asc.boss.active=Boss Active
asc.boss.defeated=Boss Defeated
asc.boss.escaped=Boss Escaped
asc.boss.health=Boss Health
asc.boss.threat_level=Threat Level
asc.boss.reward=Reward
asc.boss.vote_started=Boss spawn vote started
asc.boss.vote_passed=Vote passed - Boss spawning
asc.boss.vote_failed=Vote failed - Boss not spawning

# === RESOURCE SYSTEM ===
asc.resources.energy=Energy
asc.resources.fuel=Fuel
asc.resources.ammunition=Ammunition
asc.resources.materials=Materials
asc.resources.oxygen=Oxygen
asc.resources.water=Water
asc.resources.food=Food
asc.resources.low=Low
asc.resources.critical=Critical
asc.resources.full=Full
asc.resources.regenerating=Regenerating
asc.resources.depleted=Depleted

# === CZECH LANGUAGE SYSTEM ===
asc.czech.detected=Czech language detected
asc.czech.enabled=Czech language enabled
asc.czech.disabled=Czech language disabled
asc.czech.auto_detection=Auto-detection
asc.czech.manual_override=Manual override
asc.czech.localization_applied=Czech localization applied
asc.czech.all_systems_support=All systems now support Czech language

# === ENTITY NAMES ===
asc.entity.ship_core=Ship Core
asc.entity.hyperdrive_engine=Hyperdrive Engine
asc.entity.hyperdrive_computer=Hyperdrive Computer
asc.entity.weapon_turret=Weapon Turret
asc.entity.shield_generator=Shield Generator
asc.entity.flight_console=Flight Console
asc.entity.docking_pad=Docking Pad
asc.entity.shuttle=Shuttle
asc.entity.boss_ship=Boss Ship

# === TOOL NAMES ===
asc.tool.main_tool=ASC Main Tool
asc.tool.ship_builder=Ship Builder
asc.tool.weapon_config=Weapon Configurator
asc.tool.shield_config=Shield Configurator
asc.tool.flight_config=Flight Configurator

# === SPAWN MENU CATEGORIES ===
asc.spawnmenu.ship_components=Ship Components
asc.spawnmenu.core_systems=Core Systems
asc.spawnmenu.propulsion=Propulsion
asc.spawnmenu.weapons=Weapons
asc.spawnmenu.defense=Defense
asc.spawnmenu.utilities=Utilities
asc.spawnmenu.admin_tools=Admin Tools

# === CONTEXT MENU ===
asc.context.configure=Configure
asc.context.repair=Repair
asc.context.upgrade=Upgrade
asc.context.remove=Remove
asc.context.copy=Copy
asc.context.paste=Paste
asc.context.properties=Properties

# === HELP SYSTEM ===
asc.help.title=Advanced Space Combat Help
asc.help.getting_started=Getting Started
asc.help.ship_building=Ship Building
asc.help.combat_guide=Combat Guide
asc.help.flight_controls=Flight Controls
asc.help.ai_commands=AI Commands
asc.help.troubleshooting=Troubleshooting
asc.help.advanced_features=Advanced Features

# === PERFORMANCE ===
asc.performance.fps=FPS
asc.performance.ping=Ping
asc.performance.memory=Memory Usage
asc.performance.entities=Entity Count
asc.performance.optimization=Optimization
asc.performance.low_performance=Low Performance Detected
asc.performance.optimizing=Optimizing Performance

# === MULTIPLAYER ===
asc.multiplayer.connecting=Connecting to Server
asc.multiplayer.connected=Connected to Server
asc.multiplayer.disconnected=Disconnected from Server
asc.multiplayer.player_joined=Player Joined
asc.multiplayer.player_left=Player Left
asc.multiplayer.sync_status=Synchronization Status
asc.multiplayer.lag_detected=Network Lag Detected

# === WORKSHOP INTEGRATION ===
asc.workshop.downloading=Downloading Workshop Content
asc.workshop.download_complete=Download Complete
asc.workshop.download_failed=Download Failed
asc.workshop.missing_content=Missing Workshop Content
asc.workshop.content_loaded=Workshop Content Loaded

# === ENTITY SPECIFIC LOCALIZATION ===
# Ship Core Entity
asc.entity.asc_ship_core.name=ASC Ship Core
asc.entity.asc_ship_core.purpose=Central command and control system for ships with ARIA-4 AI integration
asc.entity.asc_ship_core.instructions=Mandatory ship core for hyperdrive operation. Auto-links to nearby components. Use E to access interface. Compatible with ultimate engine system.

# Hyperdrive Master Engine
asc.entity.hyperdrive_master_engine.name=Ultimate Hyperdrive Engine
asc.entity.hyperdrive_master_engine.purpose=Ultimate combined engine: All engine types unified with complete integration
asc.entity.hyperdrive_master_engine.instructions=The most advanced hyperdrive engine combining ALL engine types and systems. Use to access master control interface.

# Hyperdrive Computer
asc.entity.hyperdrive_computer.name=ASC Enhanced Navigation Computer
asc.entity.hyperdrive_computer.purpose=Enhanced hyperdrive control system with 4-stage travel, fleet coordination, and quantum mechanics
asc.entity.hyperdrive_computer.instructions=Enhanced navigation computer with web research improvements. Press E to open interface. Features quantum entanglement, spatial folding, and Stargate-themed travel.

# Shield Generator
asc.entity.asc_shield_generator.name=ASC Shield Generator
asc.entity.asc_shield_generator.purpose=Advanced shield generation system with multiple protection modes
asc.entity.asc_shield_generator.instructions=Generates protective energy shields around ships. Link to ship core for automatic operation.

# Hyperdrive Shield Generator
asc.entity.hyperdrive_shield_generator.name=Hyperdrive Shield Generator
asc.entity.hyperdrive_shield_generator.purpose=Hyperdrive-integrated shield generation system
asc.entity.hyperdrive_shield_generator.instructions=Shield generator with hyperdrive integration. Provides enhanced protection during FTL travel.

# Weapon Systems
asc.entity.asc_plasma_cannon.name=ASC Plasma Cannon
asc.entity.asc_plasma_cannon.purpose=High-energy plasma weapon system
asc.entity.asc_plasma_cannon.instructions=Advanced plasma weapon. Link to ship core for targeting and fire control.

asc.entity.asc_railgun.name=ASC Railgun
asc.entity.asc_railgun.purpose=Electromagnetic projectile weapon system
asc.entity.asc_railgun.instructions=High-velocity kinetic weapon. Requires ammunition and power supply.

asc.entity.asc_pulse_cannon.name=ASC Pulse Cannon
asc.entity.asc_pulse_cannon.purpose=Energy pulse weapon system
asc.entity.asc_pulse_cannon.instructions=Rapid-fire energy weapon. Effective against shields and light armor.

# Docking Systems
asc.entity.hyperdrive_docking_pad.name=Hyperdrive Docking Pad
asc.entity.hyperdrive_docking_pad.purpose=Ship docking and maintenance platform
asc.entity.hyperdrive_docking_pad.instructions=Provides docking facilities for ships. Supports refueling and repairs.

asc.entity.hyperdrive_docking_bay.name=Hyperdrive Docking Bay
asc.entity.hyperdrive_docking_bay.purpose=Large ship docking facility
asc.entity.hyperdrive_docking_bay.instructions=Advanced docking facility for large vessels. Includes automated systems.

# Flight Systems
asc.entity.hyperdrive_flight_console.name=Hyperdrive Flight Console
asc.entity.hyperdrive_flight_console.purpose=Ship flight control and navigation system
asc.entity.hyperdrive_flight_console.instructions=Primary flight control interface. Use E to access piloting controls.

# Shuttle
asc.entity.hyperdrive_shuttle.name=Hyperdrive Shuttle
asc.entity.hyperdrive_shuttle.purpose=Small transport vessel with hyperdrive capability
asc.entity.hyperdrive_shuttle.instructions=Compact ship for short-range transport. Includes basic hyperdrive system.

# Ancient Technology
asc.entity.asc_ancient_drone.name=Ancient Drone
asc.entity.asc_ancient_drone.purpose=Autonomous defense drone with ancient technology
asc.entity.asc_ancient_drone.instructions=Advanced AI-controlled defense unit. Automatically engages hostile targets.

asc.entity.asc_ancient_zpm.name=Ancient ZPM
asc.entity.asc_ancient_zpm.purpose=Zero Point Module - unlimited energy source
asc.entity.asc_ancient_zpm.instructions=Ancient power source providing massive energy output. Handle with extreme care.

# Torpedo Systems
asc.entity.hyperdrive_torpedo_launcher.name=Hyperdrive Torpedo Launcher
asc.entity.hyperdrive_torpedo_launcher.purpose=Heavy torpedo weapon system
asc.entity.hyperdrive_torpedo_launcher.instructions=Long-range heavy weapon. Requires torpedo ammunition.

# Wire Controller
asc.entity.hyperdrive_wire_controller.name=Hyperdrive Wire Controller
asc.entity.hyperdrive_wire_controller.purpose=Wiremod integration controller for hyperdrive systems
asc.entity.hyperdrive_wire_controller.instructions=Provides Wiremod connectivity for automated control systems.

# === TOOL LOCALIZATION ===
# Main Tool
tool.asc_main_tool.name=ASC Main Tool
tool.asc_main_tool.desc=Advanced Space Combat entity spawning tool
tool.asc_main_tool.0=Left click to spawn, right click to configure, reload to remove

# Ship Core Tool
tool.asc_ship_core_tool.name=ASC Ship Core Tool v6.0.0
tool.asc_ship_core_tool.desc=Spawn and configure Advanced Space Combat ship cores
tool.asc_ship_core_tool.0=Left click to spawn ship core, Right click to configure

# Hyperdrive Tool
tool.asc_hyperdrive_tool.name=ASC Enhanced Master Engine Tool v6.0.0
tool.asc_hyperdrive_tool.desc=Spawn and configure Enhanced Hyperdrive Master Engines with 4-stage travel
tool.asc_hyperdrive_tool.0=Left click to spawn enhanced master engine, Right click to link to ship core

# Shield Tool
tool.asc_shield_tool.name=Shield Tool
tool.asc_shield_tool.desc=Spawn and configure CAP-integrated shields
tool.asc_shield_tool.0=Left click to spawn shield, Right click to configure

# Ship Builder Tool
tool.asc_ship_builder.name=Ship Builder
tool.asc_ship_builder.desc=Advanced ship construction and management tool
tool.asc_ship_builder.0=Left click to place component, right click to link, reload for status

# === CATEGORIES ===
asc.category.advanced_space_combat=Advanced Space Combat
asc.category.hyperdrive=Hyperdrive
asc.category.ship_cores=Advanced Space Combat - Ship Cores
asc.category.weapons_systems=Advanced Space Combat - Weapons
asc.category.defense_systems=Advanced Space Combat - Defense
asc.category.flight_systems=Advanced Space Combat - Flight
asc.category.docking_systems=Advanced Space Combat - Docking
asc.category.ancient_technology=Advanced Space Combat - Ancient Tech
