--[[
    Advanced Space Combat - Shield Bubble Shared
]]

ENT.Type = "anim"
ENT.Base = "base_gmodentity"

ENT.PrintName = "ASC Shield Bubble"
ENT.Author = "Advanced Space Combat"
ENT.Contact = ""
ENT.Purpose = "Visual shield bubble effect"
ENT.Instructions = ""

ENT.Spawnable = false
ENT.AdminSpawnable = false
ENT.Category = "Advanced Space Combat - Effects"

-- Network variables
function ENT:SetupDataTables()
    self:NetworkVar("Float", 0, "ShieldRadius")
    self:NetworkVar("Float", 1, "ShieldStrength")
    self:NetworkVar("Float", 2, "MaxShieldStrength")
    self:NetworkVar("Vector", 0, "ShieldColor")
    self:NetworkVar("Float", 3, "ShieldAlpha")
    self:NetworkVar("Bool", 0, "Active")
    self:NetworkVar("Entity", 0, "ShieldGenerator")
    self:NetworkVar("Float", 4, "PulseIntensity")
    self:NetworkVar("Float", 5, "DamageFlash")
    self:NetworkVar("Float", 6, "LastDamageTime")
end
