-- Advanced Space Combat - Enhanced Pulse Cannon
-- High-frequency energy weapon system with advanced targeting

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

-- Inherit from base weapon class
ENT.Base = "asc_weapon_base"

-- Initialize enhanced pulse cannon
function ENT:Initialize()
    -- Set pulse cannon specific properties
    self.WeaponName = "Pulse Cannon"
    self.WeaponType = "pulse_cannon"

    -- Use centralized model manager
    if ASC and ASC.Models and ASC.Models.GetModel then
        self.WeaponModel = ASC.Models.GetModel("pulse_cannon")
    else
        self.WeaponModel = "models/props_c17/oildrum001_explosive.mdl"
    end

    self.WeaponMass = 150

    -- Enhanced combat properties
    self.Damage = 85 -- Increased damage
    self.Range = 3000 -- Increased range
    self.FireRate = 2.5 -- Increased fire rate (shots per second)
    self.EnergyConsumption = 20 -- Reduced energy consumption
    self.ProjectileSpeed = 2500
    self.ProjectileType = "pulse_bolt"
    self.Accuracy = 0.95

    -- Heat management
    self.MaxHeat = 100
    self.HeatPerShot = 12
    self.CooldownRate = 30
    self.OverheatThreshold = 85

    -- Ammunition (unlimited energy)
    self.MaxAmmo = -1
    self.AmmoType = "energy"

    -- Enhanced sounds
    self.Sounds = {
        fire = "weapons/physcannon/energy_sing_loop4.wav",
        overheat = "ambient/energy/spark6.wav",
        cooldown = "ambient/energy/weld2.wav",
        charge = "ambient/energy/weld1.wav"
    }

    -- Call base initialization
    self.BaseClass.Initialize(self)

    -- Set health
    self:SetMaxHealth(250)
    self:SetHealth(250)

    print("[ASC Pulse Cannon] Enhanced pulse cannon initialized")
end

-- Enhanced projectile creation for pulse cannon
function ENT:CreateProjectile(target)
    local firePos = self:GetPos() + self:GetForward() * 50
    local fireDir = self:GetForward()

    -- Calculate aim direction with predictive targeting
    if IsValid(target) then
        local targetPos = target:GetPos()
        local targetVel = target:GetVelocity()

        -- Predictive targeting
        if targetVel:Length() > 0 then
            local distance = firePos:Distance(targetPos)
            local timeToTarget = distance / self.ProjectileSpeed
            local predictedPos = targetPos + (targetVel * timeToTarget)
            fireDir = (predictedPos - firePos):GetNormalized()
        else
            fireDir = (targetPos - firePos):GetNormalized()
        end
    end

    -- Create pulse projectile
    local projectile = ents.Create("asc_pulse_projectile")
    if IsValid(projectile) then
        projectile:SetPos(firePos)
        projectile:SetAngles(fireDir:Angle())
        projectile:SetOwner(self:GetOwner())
        projectile:SetNWEntity("Weapon", self)
        projectile:Spawn()
        projectile:Activate()

        -- Set enhanced projectile properties
        if projectile.SetDamage then
            projectile:SetDamage(self.Damage)
        end
        if projectile.SetSpeed then
            projectile:SetSpeed(self.ProjectileSpeed)
        end
        if projectile.SetLifetime then
            projectile:SetLifetime(3.0)
        end

        -- Set velocity
        local phys = projectile:GetPhysicsObject()
        if IsValid(phys) then
            phys:SetVelocity(fireDir * self.ProjectileSpeed)
        end

        return true
    else
        -- Fallback: Create instant hit effect
        return self:CreateInstantHitEffect(firePos, fireDir, target)
    end
end

-- Create instant hit effect if projectile entity doesn't exist
function ENT:CreateInstantHitEffect(startPos, direction, target)
    local endPos = startPos + (direction * self.Range)

    -- Trace for hit detection
    local trace = util.TraceLine({
        start = startPos,
        endpos = endPos,
        filter = function(ent)
            return ent ~= self and ent ~= self.ShipCore
        end
    })

    if trace.Hit and IsValid(trace.Entity) then
        -- Apply damage
        local dmgInfo = DamageInfo()
        dmgInfo:SetDamage(self.Damage)
        dmgInfo:SetAttacker(self)
        dmgInfo:SetInflictor(self)
        dmgInfo:SetDamagePosition(trace.HitPos)
        dmgInfo:SetDamageForce(direction * self.Damage * 10)
        dmgInfo:SetDamageType(DMG_ENERGYBEAM)

        trace.Entity:TakeDamage(dmgInfo)

        -- Update statistics
        self.TotalDamage = self.TotalDamage + self.Damage
        self.TotalHits = self.TotalHits + 1

        -- Create impact effect
        local effectData = EffectData()
        effectData:SetOrigin(trace.HitPos)
        effectData:SetNormal(trace.HitNormal)
        effectData:SetMagnitude(self.Damage)
        util.Effect("asc_pulse_impact", effectData)
    end

    -- Create beam effect
    local effectData = EffectData()
    effectData:SetStart(startPos)
    effectData:SetOrigin(trace.HitPos or endPos)
    effectData:SetNormal(direction)
    effectData:SetMagnitude(self.Damage)
    util.Effect("asc_pulse_beam", effectData)

    return true
end

-- Enhanced fire effects
function ENT:OnFire(target)
    -- Enhanced firing sound
    self:EmitSound(self.Sounds.fire, 80, math.random(95, 105))

    -- Create muzzle flash effect
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos() + self:GetForward() * 50)
    effectData:SetAngles(self:GetAngles())
    effectData:SetEntity(self)
    effectData:SetScale(1.2)
    util.Effect("asc_pulse_muzzle", effectData)

    -- Create dynamic light
    local dlight = DynamicLight(self:EntIndex())
    if dlight then
        dlight.pos = self:GetPos()
        dlight.r = 100
        dlight.g = 150
        dlight.b = 255
        dlight.brightness = 3
        dlight.decay = 1000
        dlight.size = 150
        dlight.dietime = CurTime() + 0.2
    end

    -- Screen shake for nearby players
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:GetPos():Distance(self:GetPos()) < 500 then
            ply:ViewPunch(Angle(math.random(-1, 1), math.random(-1, 1), 0))
        end
    end
end

-- Enhanced target validation for pulse cannon
function ENT:IsValidTarget(ent)
    -- Call base validation first
    if not self.BaseClass.IsValidTarget(self, ent) then return false end

    -- Pulse cannon specific targeting preferences
    local class = ent:GetClass()

    -- Prefer energy-based targets (shields, energy weapons)
    if string.find(class, "shield") or string.find(class, "energy") then
        return true
    end

    -- Standard targeting
    return true
end

-- Enhanced target priority calculation
function ENT:CalculateTargetPriority(ent, distance)
    -- Get base priority
    local priority = self.BaseClass.CalculateTargetPriority(self, ent, distance)

    -- Pulse cannon bonuses
    local class = ent:GetClass()

    -- Bonus for shield generators (pulse weapons are effective against shields)
    if string.find(class, "shield") then
        priority = priority * 1.5
    end

    -- Bonus for energy-based systems
    if string.find(class, "energy") or string.find(class, "power") then
        priority = priority * 1.3
    end

    -- Bonus for smaller, faster targets (pulse weapons are good for precision)
    local velocity = ent:GetVelocity():Length()
    if velocity > 200 then
        priority = priority * 1.2
    end

    return priority
end

-- Enhanced overheat effects
function ENT:OnOverheat()
    self.BaseClass.OnOverheat(self)

    -- Pulse cannon specific overheat effects
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos())
    effectData:SetMagnitude(3)
    util.Effect("asc_pulse_overload", effectData)

    -- Electrical discharge effects
    for i = 1, 5 do
        timer.Simple(i * 0.2, function()
            if IsValid(self) then
                local sparkPos = self:GetPos() + VectorRand() * 30
                local effectData = EffectData()
                effectData:SetOrigin(sparkPos)
                effectData:SetMagnitude(1)
                util.Effect("ElectricSpark", effectData)
            end
        end)
    end
end

-- Enhanced cooldown effects
function ENT:OnCooldown()
    self.BaseClass.OnCooldown(self)

    -- Pulse cannon specific cooldown effects
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos())
    effectData:SetMagnitude(1)
    util.Effect("asc_pulse_cooldown", effectData)
end

-- Get pulse cannon specific status
function ENT:GetWeaponStatus()
    local status = self.BaseClass.GetWeaponStatus(self)

    -- Add pulse cannon specific data
    status.weaponClass = "Enhanced Pulse Cannon"
    status.specialization = "Anti-Shield/Energy Systems"
    status.effectiveRange = self.Range
    status.optimalRange = self.Range * 0.7
    status.chargeTime = 0 -- Instant fire
    status.burstMode = false

    return status
end
