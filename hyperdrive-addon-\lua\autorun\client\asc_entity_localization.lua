-- Advanced Space Combat - Entity Localization System v1.0.0
-- Applies proper localization to entity names, categories, and descriptions

print("[Advanced Space Combat] Entity Localization System v1.0.0 Loading...")

-- Initialize namespace
ASC = ASC or {}
ASC.EntityLocalization = ASC.EntityLocalization or {}

-- Configuration
ASC.EntityLocalization.Config = {
    Enabled = true,
    LogUpdates = false,
    UpdateInterval = 1, -- seconds
    ForceUpdate = false
}

-- Get localized text with fallback - simplified
ASC.EntityLocalization.GetText = function(key, fallback)
    -- Use the new simple localization system
    if ASC.Localization and ASC.Localization.GetText then
        return ASC.Localization.GetText(key, fallback or key)
    end

    -- Fallback to Czech system if available
    if ASC.Czech and ASC.Czech.GetText then
        -- Convert key format for Czech system
        local czechKey = key:gsub("^asc%.", ""):gsub("%.", "_"):gsub("_", " ")
        return ASC.Czech.GetText(czechKey, fallback)
    end

    -- Final fallback
    return fallback or key
end

-- Entity localization mappings
ASC.EntityLocalization.EntityMappings = {
    -- Core Systems
    ["ship_core"] = {
        name_key = "asc.entity.ship_core",
        category_key = "asc.category.ship_components",
        fallback_name = "Ship Core"
    },
    ["asc_ship_core"] = {
        name_key = "asc.entity.ship_core",
        category_key = "asc.category.ship_components",
        fallback_name = "ASC Ship Core"
    },
    
    -- Hyperdrive Systems
    ["hyperdrive_engine"] = {
        name_key = "asc.entity.hyperdrive_engine",
        category_key = "asc.category.ship_components",
        fallback_name = "Hyperdrive Engine"
    },
    ["hyperdrive_master_engine"] = {
        name_key = "asc.entity.hyperdrive_engine",
        category_key = "asc.category.ship_components",
        fallback_name = "Master Hyperdrive Engine"
    },
    ["hyperdrive_computer"] = {
        name_key = "asc.entity.hyperdrive_computer",
        category_key = "asc.category.ship_components",
        fallback_name = "Hyperdrive Computer"
    },
    
    -- Weapons
    ["asc_pulse_cannon"] = {
        name_key = "asc.entity.weapon_turret",
        category_key = "asc.category.weapons",
        fallback_name = "Pulse Cannon"
    },
    ["asc_plasma_cannon"] = {
        name_key = "asc.entity.weapon_turret",
        category_key = "asc.category.weapons",
        fallback_name = "Plasma Cannon"
    },
    ["asc_railgun"] = {
        name_key = "asc.entity.weapon_turret",
        category_key = "asc.category.weapons",
        fallback_name = "Railgun"
    },
    
    -- Other Systems
    ["asc_shield_generator"] = {
        name_key = "asc.entity.shield_generator",
        category_key = "asc.category.ship_components",
        fallback_name = "Shield Generator"
    },
    ["asc_flight_console"] = {
        name_key = "asc.entity.flight_console",
        category_key = "asc.category.ship_components",
        fallback_name = "Flight Console"
    },
    ["asc_docking_pad"] = {
        name_key = "asc.entity.docking_pad",
        category_key = "asc.category.utilities",
        fallback_name = "Docking Pad"
    }
}

-- Tool localization mappings
ASC.EntityLocalization.ToolMappings = {
    ["asc_main_tool"] = {
        name_key = "asc.tool.main_tool",
        fallback_name = "ASC Main Tool"
    },
    ["asc_ship_builder"] = {
        name_key = "asc.tool.ship_builder",
        fallback_name = "Ship Builder"
    },
    ["asc_weapon_config"] = {
        name_key = "asc.tool.weapon_config",
        fallback_name = "Weapon Configurator"
    },
    ["asc_shield_config"] = {
        name_key = "asc.tool.shield_config",
        fallback_name = "Shield Configurator"
    },
    ["asc_flight_config"] = {
        name_key = "asc.tool.flight_config",
        fallback_name = "Flight Configurator"
    }
}

-- Update entity localization in spawn menu
function ASC.EntityLocalization.UpdateEntityLocalization()
    if not ASC.EntityLocalization.Config.Enabled then return end
    
    local updatedCount = 0
    
    -- Update entities
    for entityClass, mapping in pairs(ASC.EntityLocalization.EntityMappings) do
        local spawnableData = list.Get("SpawnableEntities")[entityClass]
        if spawnableData then
            local localizedName = ASC.EntityLocalization.GetText(mapping.name_key, mapping.fallback_name)
            local localizedCategory = ASC.EntityLocalization.GetText(mapping.category_key, spawnableData.Category)
            
            if spawnableData.PrintName ~= localizedName then
                spawnableData.PrintName = localizedName
                updatedCount = updatedCount + 1
                
                if ASC.EntityLocalization.Config.LogUpdates then
                    print("[Entity Localization] Updated " .. entityClass .. " name: " .. localizedName)
                end
            end
            
            -- Update category if we have a localized version
            if localizedCategory and localizedCategory ~= mapping.category_key then
                local baseCategoryName = spawnableData.Category:match("Advanced Space Combat %- (.+)") or spawnableData.Category
                spawnableData.Category = "Advanced Space Combat - " .. localizedCategory
            end
        end
    end
    
    -- Update tools
    for toolClass, mapping in pairs(ASC.EntityLocalization.ToolMappings) do
        local toolData = list.Get("ToolMenus")
        -- Tool localization would need to be handled differently
        -- This is a placeholder for tool name updates
    end
    
    if updatedCount > 0 and ASC.EntityLocalization.Config.LogUpdates then
        print("[Entity Localization] Updated " .. updatedCount .. " entity names")
    end
end

-- Hook into language change events
hook.Add("Think", "ASC_EntityLocalization_Update", function()
    -- Only update periodically to avoid performance issues
    if not ASC.EntityLocalization.LastUpdate then
        ASC.EntityLocalization.LastUpdate = CurTime()
    end
    
    if CurTime() - ASC.EntityLocalization.LastUpdate >= ASC.EntityLocalization.Config.UpdateInterval then
        ASC.EntityLocalization.UpdateEntityLocalization()
        ASC.EntityLocalization.LastUpdate = CurTime()
    end
end)

-- Initialize when localization systems are ready
timer.Simple(3, function()
    ASC.EntityLocalization.UpdateEntityLocalization()
    print("[Entity Localization] Initial localization update complete")
end)

-- Force update when language changes
hook.Add("OnLanguageChanged", "ASC_EntityLocalization_LanguageChange", function()
    timer.Simple(0.1, function()
        ASC.EntityLocalization.UpdateEntityLocalization()
        print("[Entity Localization] Language change detected, updating entity names")
    end)
end)

print("[Advanced Space Combat] Entity Localization System v1.0.0 loaded")
