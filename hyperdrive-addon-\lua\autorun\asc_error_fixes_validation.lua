--[[
    Advanced Space Combat - Error Fixes Validation System v1.0.0
    
    Comprehensive validation system for recent error fixes:
    1. CAP Validation Fallback error fix
    2. GMod Language command blocked error fix
]]

-- Initialize Error Fixes Validation namespace
ASC = ASC or {}
ASC.ErrorFixesValidation = ASC.ErrorFixesValidation or {}

-- Configuration
ASC.ErrorFixesValidation.Config = {
    Enabled = true,
    AutoTest = true,
    LogResults = true,
    TestInterval = 60 -- seconds
}

-- Test results storage
ASC.ErrorFixesValidation.Results = {
    lastTest = 0,
    capValidationFix = false,
    gmodLanguageFix = false,
    overallStatus = "UNKNOWN"
}

-- Test CAP Validation Fallback Fix
function ASC.ErrorFixesValidation.TestCAPValidationFix()
    print("[Error Fixes] Testing CAP Validation Fallback fix...")
    
    local testResults = {
        passed = 0,
        failed = 0,
        errors = {},
        warnings = {}
    }
    
    -- Test 1: Check if ASC.CAP.Fallback exists and what type it is
    if ASC.CAP and ASC.CAP.Fallback ~= nil then
        local fallbackType = type(ASC.CAP.Fallback)
        print("[Error Fixes] CAP.Fallback type: " .. fallbackType)
        
        if fallbackType == "table" or fallbackType == "boolean" then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ CAP.Fallback has valid type")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "CAP.Fallback has invalid type: " .. fallbackType)
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "CAP.Fallback not found")
    end
    
    -- Test 2: Try to run the CAP validation function that was causing errors
    if ASC.CAP and ASC.CAP.Validation and ASC.CAP.Validation.Tests and ASC.CAP.Validation.Tests.FallbackSystem then
        local success, result = pcall(ASC.CAP.Validation.Tests.FallbackSystem)

        if success then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ CAP FallbackSystem test runs without errors")

            if result and result.errors and #result.errors == 0 then
                print("[Error Fixes] ✓ CAP FallbackSystem test passed completely")
            else
                table.insert(testResults.warnings, "CAP FallbackSystem test has warnings but no errors")
            end
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "CAP FallbackSystem test failed: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "CAP FallbackSystem test function not found")
    end

    -- Test 2.5: Try to run the full CAP validation that was causing GenerateSummary errors
    if ASC.CAP and ASC.CAP.Validation and ASC.CAP.Validation.RunValidation then
        local success, result = pcall(ASC.CAP.Validation.RunValidation)

        if success then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ CAP RunValidation (including GenerateSummary) runs without errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "CAP RunValidation failed: " .. tostring(result))
        end
    else
        table.insert(testResults.warnings, "CAP RunValidation function not found")
    end
    
    -- Test 3: Check if the specific error line is fixed
    if ASC.CAP and ASC.CAP.Fallback then
        local success, result = pcall(function()
            -- This was the problematic line: ASC.CAP.Fallback.Resources
            if type(ASC.CAP.Fallback) == "table" then
                local resources = ASC.CAP.Fallback.Resources
                return true
            else
                return true -- Boolean mode, should not try to access .Resources
            end
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ Fallback.Resources access is safe")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "Fallback.Resources access still causes errors: " .. tostring(result))
        end
    end
    
    local capFixWorking = testResults.failed == 0
    ASC.ErrorFixesValidation.Results.capValidationFix = capFixWorking
    
    print("[Error Fixes] CAP Validation Fix: " .. (capFixWorking and "✓ WORKING" or "✗ FAILED"))
    return capFixWorking, testResults
end

-- Test GMod Language Command Fix
function ASC.ErrorFixesValidation.TestGModLanguageFix()
    print("[Error Fixes] Testing GMod Language command fix...")
    
    local testResults = {
        passed = 0,
        failed = 0,
        errors = {},
        warnings = {}
    }
    
    -- Test 1: Check if GMod localization system exists
    if ASC.GMod and ASC.GMod.Localization then
        testResults.passed = testResults.passed + 1
        print("[Error Fixes] ✓ GMod Localization system found")
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "GMod Localization system not found")
        return false, testResults
    end
    
    -- Test 2: Try to call SetLanguage function (this was causing the blocked command error)
    if ASC.GMod.Localization.SetLanguage then
        local success, result = pcall(function()
            -- Try to set language to Czech (this should not call blocked gmod_language command)
            return ASC.GMod.Localization.SetLanguage("cs")
        end)

        if success then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ SetLanguage function runs without blocked command error")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "SetLanguage still causes errors: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "SetLanguage function not found")
    end

    -- Test 2.5: Try to call GetText function (this was causing the language global error)
    if ASC.GMod.Localization.GetText then
        local success, result = pcall(function()
            -- Try to get text (this should not access undefined language global)
            return ASC.GMod.Localization.GetText("test.key", "fallback")
        end)

        if success then
            testResults.passed = testResults.passed + 1
            print("[Error Fixes] ✓ GetText function runs without language global error")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "GetText still causes errors: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "GetText function not found")
    end
    
    -- Test 3: Check if localization integration works (including new language API fix)
    if ASC.LocalizationIntegration then
        -- Test the Validate function that was causing the language.GetPhrase error
        if ASC.LocalizationIntegration.Validate then
            local success, result = pcall(function()
                return ASC.LocalizationIntegration.Validate()
            end)

            if success then
                testResults.passed = testResults.passed + 1
                print("[Error Fixes] ✓ Localization integration Validate function works")
            else
                testResults.failed = testResults.failed + 1
                table.insert(testResults.errors, "Localization integration Validate still fails: " .. tostring(result))
            end
        end

        -- Test AutoSetCzechForPlayers function
        if ASC.LocalizationIntegration.AutoSetCzechForPlayers then
            local success, result = pcall(function()
                -- This function was calling the problematic SetLanguage and GetConVar
                return ASC.LocalizationIntegration.AutoSetCzechForPlayers()
            end)

            if success then
                testResults.passed = testResults.passed + 1
                print("[Error Fixes] ✓ AutoSetCzechForPlayers function safe")
            else
                testResults.failed = testResults.failed + 1
                table.insert(testResults.errors, "AutoSetCzechForPlayers still has issues: " .. tostring(result))
            end
        end
    else
        table.insert(testResults.warnings, "Localization integration not found (optional)")
    end
    
    local gmodFixWorking = testResults.failed == 0
    ASC.ErrorFixesValidation.Results.gmodLanguageFix = gmodFixWorking
    
    print("[Error Fixes] GMod Language Fix: " .. (gmodFixWorking and "✓ WORKING" or "✗ FAILED"))
    return gmodFixWorking, testResults
end

-- Run all error fix tests
function ASC.ErrorFixesValidation.RunAllTests()
    print("[Error Fixes] ========================================")
    print("[Error Fixes] Running Error Fixes Validation Tests")
    print("[Error Fixes] ========================================")
    
    local startTime = CurTime()
    
    -- Test CAP Validation Fix
    local capWorking, capResults = ASC.ErrorFixesValidation.TestCAPValidationFix()
    
    -- Test GMod Language Fix
    local gmodWorking, gmodResults = ASC.ErrorFixesValidation.TestGModLanguageFix()
    
    -- Calculate overall status
    local allWorking = capWorking and gmodWorking
    ASC.ErrorFixesValidation.Results.overallStatus = allWorking and "ALL_FIXED" or "SOME_ISSUES"
    ASC.ErrorFixesValidation.Results.lastTest = CurTime()
    
    local testTime = CurTime() - startTime
    
    -- Print summary
    print("[Error Fixes] ========================================")
    print("[Error Fixes] ERROR FIXES VALIDATION SUMMARY")
    print("[Error Fixes] ========================================")
    print("[Error Fixes] CAP Validation Fix: " .. (capWorking and "✅ WORKING" or "❌ FAILED"))
    print("[Error Fixes] GMod Language Fix: " .. (gmodWorking and "✅ WORKING" or "❌ FAILED"))
    print("[Error Fixes] Overall Status: " .. (allWorking and "✅ ALL FIXES WORKING" or "❌ SOME ISSUES REMAIN"))
    print("[Error Fixes] Test Duration: " .. string.format("%.3fs", testTime))
    
    -- Show any remaining errors
    local totalErrors = {}
    if capResults.errors then
        for _, error in ipairs(capResults.errors) do
            table.insert(totalErrors, "CAP: " .. error)
        end
    end
    if gmodResults.errors then
        for _, error in ipairs(gmodResults.errors) do
            table.insert(totalErrors, "GMod: " .. error)
        end
    end
    
    if #totalErrors > 0 then
        print("[Error Fixes] REMAINING ERRORS:")
        for _, error in ipairs(totalErrors) do
            print("[Error Fixes]   - " .. error)
        end
    else
        print("[Error Fixes] 🎉 NO ERRORS DETECTED!")
    end
    
    print("[Error Fixes] ========================================")
    
    return allWorking
end

-- Console command for manual testing
concommand.Add("asc_test_error_fixes", function(ply, cmd, args)
    local success = ASC.ErrorFixesValidation.RunAllTests()
    
    local msg = "[Error Fixes] Validation " .. (success and "✅ PASSED - All fixes working!" or "❌ FAILED - Some issues remain")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Console command for quick status
concommand.Add("asc_error_fixes_status", function(ply, cmd, args)
    local results = ASC.ErrorFixesValidation.Results
    
    local msg = "[Error Fixes] Status: " .. results.overallStatus .. 
                " | CAP: " .. (results.capValidationFix and "✅" or "❌") ..
                " | GMod: " .. (results.gmodLanguageFix and "✅" or "❌")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Auto-test on load
if ASC.ErrorFixesValidation.Config.AutoTest then
    timer.Simple(3, function()
        print("[Error Fixes] Running automatic error fixes validation...")
        ASC.ErrorFixesValidation.RunAllTests()
    end)
end

-- Periodic testing
if ASC.ErrorFixesValidation.Config.TestInterval > 0 then
    timer.Create("ASC_ErrorFixesValidation", ASC.ErrorFixesValidation.Config.TestInterval, 0, function()
        if ASC.ErrorFixesValidation.Config.LogResults then
            print("[Error Fixes] Running periodic validation...")
        end
        ASC.ErrorFixesValidation.RunAllTests()
    end)
end

print("[Advanced Space Combat] Error Fixes Validation System v1.0.0 Loaded")
