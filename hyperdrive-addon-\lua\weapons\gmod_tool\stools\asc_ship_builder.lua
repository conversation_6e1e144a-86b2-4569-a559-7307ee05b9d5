-- Advanced Space Combat - Ship Builder Tool v3.1.0
-- Specialized tool for ship construction and management

TOOL.Category = "Advanced Space Combat"
TOOL.Name = "Ship Builder"
TOOL.Command = nil
TOOL.ConfigName = ""

-- Tool information
TOOL.Information = {
    { name = "left", text = "Place ship component" },
    { name = "right", text = "Link to ship core" },
    { name = "reload", text = "Show ship status" }
}

-- Client variables
if CLIENT then
    TOOL.ClientConVar["component_type"] = "asc_ship_core"
    TOOL.ClientConVar["auto_wire"] = "1"
    TOOL.ClientConVar["ship_name"] = "New Ship"
    TOOL.ClientConVar["build_mode"] = "standard"
    TOOL.ClientConVar["template_name"] = "Fighter"
    TOOL.ClientConVar["component_spacing"] = "150"
    TOOL.ClientConVar["auto_link"] = "1"
end

-- Ship component templates (Fixed to use correct ship core class)
local ShipTemplates = {
    ["Fighter"] = {
        {class = "asc_ship_core", pos = Vector(0, 0, 0)},
        {class = "hyperdrive_engine", pos = Vector(-100, 0, 0)},
        {class = "asc_pulse_cannon", pos = Vector(50, 30, 0)},
        {class = "asc_pulse_cannon", pos = Vector(50, -30, 0)},
        {class = "asc_shield_generator", pos = Vector(0, 0, 50)}
    },
    ["Cruiser"] = {
        {class = "asc_ship_core", pos = Vector(0, 0, 0)},
        {class = "hyperdrive_master_engine", pos = Vector(-200, 0, 0)},
        {class = "hyperdrive_engine", pos = Vector(-150, 50, 0)},
        {class = "hyperdrive_engine", pos = Vector(-150, -50, 0)},
        {class = "asc_railgun", pos = Vector(100, 0, 0)},
        {class = "asc_plasma_cannon", pos = Vector(80, 60, 0)},
        {class = "asc_plasma_cannon", pos = Vector(80, -60, 0)},
        {class = "asc_shield_generator", pos = Vector(0, 0, 80)},
        {class = "hyperdrive_docking_pad", pos = Vector(-50, 0, -50)}
    },
    ["Carrier"] = {
        {class = "asc_ship_core", pos = Vector(0, 0, 0)},
        {class = "hyperdrive_master_engine", pos = Vector(-300, 0, 0)},
        {class = "hyperdrive_engine", pos = Vector(-250, 100, 0)},
        {class = "hyperdrive_engine", pos = Vector(-250, -100, 0)},
        {class = "hyperdrive_docking_bay", pos = Vector(0, 0, -100)},
        {class = "hyperdrive_docking_pad", pos = Vector(-100, 150, -50)},
        {class = "hyperdrive_docking_pad", pos = Vector(-100, -150, -50)},
        {class = "asc_shield_generator", pos = Vector(0, 0, 120)},
        {class = "asc_shield_generator", pos = Vector(-150, 0, 80)}
    },
    ["Corvette"] = {
        {class = "asc_ship_core", pos = Vector(0, 0, 0)},
        {class = "hyperdrive_engine", pos = Vector(-80, 0, 0)},
        {class = "asc_pulse_cannon", pos = Vector(40, 0, 0)},
        {class = "asc_shield_generator", pos = Vector(0, 0, 40)}
    },
    ["Destroyer"] = {
        {class = "asc_ship_core", pos = Vector(0, 0, 0)},
        {class = "hyperdrive_master_engine", pos = Vector(-250, 0, 0)},
        {class = "hyperdrive_engine", pos = Vector(-200, 40, 0)},
        {class = "hyperdrive_engine", pos = Vector(-200, -40, 0)},
        {class = "asc_railgun", pos = Vector(120, 0, 0)},
        {class = "asc_plasma_cannon", pos = Vector(90, 50, 0)},
        {class = "asc_plasma_cannon", pos = Vector(90, -50, 0)},
        {class = "asc_shield_generator", pos = Vector(0, 0, 100)},
        {class = "hyperdrive_computer", pos = Vector(50, 0, 0)}
    }
}

-- Component categories (Fixed to use correct entity classes)
local ComponentCategories = {
    ["Core"] = {"asc_ship_core", "hyperdrive_computer", "hyperdrive_wire_controller"},
    ["Propulsion"] = {"hyperdrive_engine", "hyperdrive_master_engine", "hyperdrive_sb_engine"},
    ["Weapons"] = {"asc_pulse_cannon", "asc_plasma_cannon", "asc_railgun", "hyperdrive_beam_weapon", "hyperdrive_torpedo_launcher"},
    ["Defense"] = {"asc_shield_generator", "hyperdrive_shield_generator"},
    ["Utility"] = {"hyperdrive_docking_pad", "hyperdrive_docking_bay", "hyperdrive_flight_console", "hyperdrive_beacon"}
}

-- Get all available components
local function GetAllComponents()
    local components = {}
    for category, comps in pairs(ComponentCategories) do
        for _, comp in ipairs(comps) do
            table.insert(components, comp)
        end
    end
    return components
end

-- Get ship core for player (Fixed to use correct class name)
function TOOL:GetPlayerShipCore(ply)
    -- Look for ASC ship cores first
    local shipCores = ents.FindByClass("asc_ship_core")

    -- Also check for legacy ship cores for compatibility
    local legacyCores = ents.FindByClass("ship_core")
    for _, core in ipairs(legacyCores) do
        table.insert(shipCores, core)
    end

    local closestCore = nil
    local closestDistance = math.huge

    for _, core in ipairs(shipCores) do
        if IsValid(core) then
            local owner = core.CPPIGetOwner and core:CPPIGetOwner() or core:GetOwner()
            if IsValid(owner) and owner == ply then
                local distance = ply:GetPos():Distance(core:GetPos())
                if distance < closestDistance then
                    closestDistance = distance
                    closestCore = core
                end
            end
        end
    end

    return closestCore, closestDistance
end

-- Left click - Place component
function TOOL:LeftClick(trace)
    if CLIENT then return true end
    
    local ply = self:GetOwner()
    if not IsValid(ply) then return false end
    
    local componentType = self:GetClientInfo("component_type")
    local buildMode = self:GetClientInfo("build_mode")
    
    if buildMode == "template" then
        return self:BuildShipTemplate(trace, ply)
    else
        return self:PlaceComponent(trace, ply, componentType)
    end
end

-- Place single component with enhanced error handling
function TOOL:PlaceComponent(trace, ply, componentType)
    -- Validate inputs
    if not IsValid(ply) or not componentType or componentType == "" then
        if IsValid(ply) then
            ply:ChatPrint("[Ship Builder] Invalid component type!")
        end
        return false
    end

    -- Check if entity class exists
    if not scripted_ents.GetStored(componentType) then
        ply:ChatPrint("[Ship Builder] Component '" .. componentType .. "' not found!")
        ply:ChatPrint("[Ship Builder] Available components: asc_ship_core, hyperdrive_engine, hyperdrive_master_engine")
        return false
    end

    -- Validate spawn position
    if trace.HitSky then
        ply:ChatPrint("[Ship Builder] Cannot place components in the sky!")
        return false
    end

    -- Calculate spawn position with safety checks
    local spawnPos = trace.HitPos + trace.HitNormal * 10

    -- Validate spawn position is not inside world
    if util.IsInWorld(spawnPos) == false then
        ply:ChatPrint("[Ship Builder] Invalid spawn position!")
        return false
    end

    -- Create component with error handling
    local ent = nil
    local createSuccess, createError = pcall(function()
        ent = ents.Create(componentType)
    end)

    if not createSuccess or not IsValid(ent) then
        ply:ChatPrint("[Ship Builder] Failed to create component: " .. tostring(createError))
        return false
    end

    -- Set position and angles
    ent:SetPos(spawnPos)
    ent:SetAngles(ply:EyeAngles())

    -- Spawn with error handling
    local spawnSuccess, spawnError = pcall(function()
        ent:Spawn()
        ent:Activate()
    end)

    if not spawnSuccess then
        ply:ChatPrint("[Ship Builder] Failed to spawn component: " .. tostring(spawnError))
        if IsValid(ent) then ent:Remove() end
        return false
    end

    -- Set ownership
    if ent.CPPISetOwner then
        ent:CPPISetOwner(ply)
    elseif ent.SetOwner then
        ent:SetOwner(ply)
    end

    -- Auto-link to ship core if enabled
    if self:GetClientNumber("auto_link", 1) == 1 then
        local shipCore = self:GetPlayerShipCore(ply)
        if IsValid(shipCore) then
            if ent.SetShipCore then
                ent:SetShipCore(shipCore)
            end
            if shipCore.AddComponent then
                shipCore:AddComponent(ent)
            end

            local distance = ent:GetPos():Distance(shipCore:GetPos())
            ply:ChatPrint("[Ship Builder] Auto-linked to ship core (" .. math.floor(distance) .. " units)")

            -- Auto-wire if enabled
            if self:GetClientNumber("auto_wire", 1) == 1 and WireLib then
                self:AutoWireComponent(ent, shipCore)
            end
        elseif componentType ~= "asc_ship_core" then
            ply:ChatPrint("[Ship Builder] No ship core found for auto-linking. Place a ship core first!")
        end
    end

    -- Undo support
    undo.Create("Ship Component: " .. componentType)
    undo.AddEntity(ent)
    undo.SetPlayer(ply)
    undo.Finish()

    ply:ChatPrint("[Ship Builder] Placed " .. componentType .. " successfully!")
    return true
end

-- Build ship template
function TOOL:BuildShipTemplate(trace, ply)
    local templateName = self:GetClientInfo("template_name") or "Fighter"
    local template = ShipTemplates[templateName]
    
    if not template then
        ply:ChatPrint("[Ship Builder] Template '" .. templateName .. "' not found!")
        return false
    end
    
    local basePos = trace.HitPos + trace.HitNormal * 50
    local baseAngles = ply:EyeAngles()
    local createdEntities = {}
    local shipCore = nil
    
    -- Create all components
    for _, component in ipairs(template) do
        if scripted_ents.GetStored(component.class) then
            local ent = ents.Create(component.class)
            if IsValid(ent) then
                local worldPos = basePos + baseAngles:Forward() * component.pos.x + 
                                baseAngles:Right() * component.pos.y + 
                                baseAngles:Up() * component.pos.z
                
                ent:SetPos(worldPos)
                ent:SetAngles(baseAngles)
                ent:Spawn()
                ent:Activate()
                
                -- Set ownership
                if ent.CPPISetOwner then
                    ent:CPPISetOwner(ply)
                elseif ent.SetOwner then
                    ent:SetOwner(ply)
                end
                
                table.insert(createdEntities, ent)

                -- Check for both ASC ship core and legacy ship core
                if component.class == "asc_ship_core" or component.class == "ship_core" then
                    shipCore = ent
                    if ent.SetShipName then
                        ent:SetShipName(self:GetClientInfo("ship_name"))
                    elseif ent.SetEntityName then
                        ent:SetEntityName(self:GetClientInfo("ship_name"))
                    end
                end
            end
        end
    end
    
    -- Link all components to ship core
    if IsValid(shipCore) then
        for _, ent in ipairs(createdEntities) do
            if IsValid(ent) and ent ~= shipCore then
                if ent.SetShipCore then
                    ent:SetShipCore(shipCore)
                end
                if shipCore.AddComponent then
                    shipCore:AddComponent(ent)
                end
            end
        end
    end
    
    -- Undo support
    undo.Create("Ship Template: " .. templateName)
    for _, ent in ipairs(createdEntities) do
        undo.AddEntity(ent)
    end
    undo.SetPlayer(ply)
    undo.Finish()
    
    ply:ChatPrint("[Ship Builder] Built " .. templateName .. " template with " .. #createdEntities .. " components!")
    return true
end

-- Auto-wire component to ship core (Server-side only)
function TOOL:AutoWireComponent(component, shipCore)
    if CLIENT then return end -- Server only
    if not WireLib or not IsValid(component) or not IsValid(shipCore) then return end

    local ply = self:GetOwner()
    if not IsValid(ply) then return end

    -- Basic wire connections based on component type
    local componentClass = component:GetClass()
    local success = false

    -- Use pcall to safely attempt wire connections
    local wireSuccess, wireError = pcall(function()
        if string.find(componentClass, "engine") then
            -- Wire engines for thrust control
            if component.Inputs and shipCore.Outputs then
                if component.Inputs.Thrust and shipCore.Outputs.Thrust then
                    WireLib.Link_Start(ply, shipCore, shipCore:GetPos(), "Thrust", "normal", ply, component, component:GetPos(), "Thrust", "normal")
                    success = true
                end
            end
        elseif string.find(componentClass, "weapon") or string.find(componentClass, "cannon") then
            -- Wire weapons for firing control
            if component.Inputs and shipCore.Outputs then
                if component.Inputs.Fire and shipCore.Outputs.Fire then
                    WireLib.Link_Start(ply, shipCore, shipCore:GetPos(), "Fire", "normal", ply, component, component:GetPos(), "Fire", "normal")
                    success = true
                end
            end
        elseif string.find(componentClass, "shield") then
            -- Wire shields for power control
            if component.Inputs and shipCore.Outputs then
                if component.Inputs.Active and shipCore.Outputs.Shield then
                    WireLib.Link_Start(ply, shipCore, shipCore:GetPos(), "Shield", "normal", ply, component, component:GetPos(), "Active", "normal")
                    success = true
                end
            end
        end
    end)

    if wireSuccess and success then
        ply:ChatPrint("[Ship Builder] Auto-wired " .. componentClass .. " to ship core")
    elseif not wireSuccess then
        print("[Ship Builder] Auto-wire error: " .. tostring(wireError))
    end
end

-- Right click - Link to ship core
function TOOL:RightClick(trace)
    if CLIENT then return true end
    
    local ent = trace.Entity
    if not IsValid(ent) then return false end
    
    local ply = self:GetOwner()
    if not IsValid(ply) then return false end
    
    -- Check ownership
    local owner = ent.CPPIGetOwner and ent:CPPIGetOwner() or ent:GetOwner()
    if IsValid(owner) and owner ~= ply and not ply:IsAdmin() then
        ply:ChatPrint("[Ship Builder] You don't own this entity!")
        return false
    end
    
    -- Find ship core and link
    local shipCore = self:GetPlayerShipCore(ply)
    if IsValid(shipCore) then
        if ent.SetShipCore then
            ent:SetShipCore(shipCore)
        end
        if shipCore.AddComponent then
            shipCore:AddComponent(ent)
        end
        
        local distance = ent:GetPos():Distance(shipCore:GetPos())
        ply:ChatPrint("[Ship Builder] Linked " .. ent:GetClass() .. " to ship core (" .. math.floor(distance) .. " units)")
    else
        ply:ChatPrint("[Ship Builder] No ship core found! Spawn a ship core first.")
    end
    
    return true
end

-- Reload - Show ship status
function TOOL:Reload(trace)
    if CLIENT then return true end
    
    local ply = self:GetOwner()
    if not IsValid(ply) then return false end
    
    local shipCore, distance = self:GetPlayerShipCore(ply)
    if IsValid(shipCore) then
        local componentCount = 0
        if shipCore.GetComponents then
            local components = shipCore:GetComponents()
            componentCount = #components
        end
        
        local shipName = "Unnamed Ship"
        if shipCore.GetEntityName then
            shipName = shipCore:GetEntityName() or shipName
        end
        
        ply:ChatPrint("[Ship Builder] Ship Status:")
        ply:ChatPrint("Name: " .. shipName)
        ply:ChatPrint("Components: " .. componentCount)
        ply:ChatPrint("Distance: " .. math.floor(distance) .. " units")
        
        if shipCore.GetHealth then
            ply:ChatPrint("Health: " .. (shipCore:GetHealth() or 0) .. "/" .. (shipCore:GetMaxHealth() or 0))
        end
        if shipCore.GetEnergy then
            ply:ChatPrint("Energy: " .. (shipCore:GetEnergy() or 0) .. "/" .. (shipCore:GetMaxEnergy() or 0))
        end
    else
        ply:ChatPrint("[Ship Builder] No ship found! Spawn a ship core to start building.")
    end
    
    return true
end

if CLIENT then
    -- Build the client-side control panel
    function TOOL.BuildCPanel(panel)
        -- Build mode selection
        panel:AddControl("Header", {Text = "Ship Builder v3.1.0", Description = "Advanced ship construction tool"})

        local buildModeCombo = panel:AddControl("ComboBox", {
            Label = "Build Mode",
            MenuButton = 1,
            Folder = "asc_ship_builder",
            Options = {
                ["standard"] = {asc_ship_builder_build_mode = "standard"},
                ["template"] = {asc_ship_builder_build_mode = "template"}
            },
            CVars = {[0] = "asc_ship_builder_build_mode"}
        })

        -- Component selection for standard mode
        panel:AddControl("Header", {Text = "Component Selection"})

        local componentCombo = panel:AddControl("ComboBox", {
            Label = "Component Type",
            MenuButton = 1,
            Folder = "asc_ship_builder",
            Options = {
                ["ASC Ship Core"] = {asc_ship_builder_component_type = "asc_ship_core"},
                ["Hyperdrive Engine"] = {asc_ship_builder_component_type = "hyperdrive_engine"},
                ["Master Engine"] = {asc_ship_builder_component_type = "hyperdrive_master_engine"},
                ["Hyperdrive Computer"] = {asc_ship_builder_component_type = "hyperdrive_computer"},
                ["Pulse Cannon"] = {asc_ship_builder_component_type = "asc_pulse_cannon"},
                ["Plasma Cannon"] = {asc_ship_builder_component_type = "asc_plasma_cannon"},
                ["Railgun"] = {asc_ship_builder_component_type = "asc_railgun"},
                ["Shield Generator"] = {asc_ship_builder_component_type = "asc_shield_generator"},
                ["Docking Pad"] = {asc_ship_builder_component_type = "hyperdrive_docking_pad"},
                ["Docking Bay"] = {asc_ship_builder_component_type = "hyperdrive_docking_bay"}
            },
            CVars = {[0] = "asc_ship_builder_component_type"}
        })

        -- Template selection for template mode
        panel:AddControl("Header", {Text = "Ship Templates"})

        local templateCombo = panel:AddControl("ComboBox", {
            Label = "Template",
            MenuButton = 1,
            Folder = "asc_ship_builder",
            Options = {
                ["Fighter"] = {asc_ship_builder_template_name = "Fighter"},
                ["Corvette"] = {asc_ship_builder_template_name = "Corvette"},
                ["Cruiser"] = {asc_ship_builder_template_name = "Cruiser"},
                ["Destroyer"] = {asc_ship_builder_template_name = "Destroyer"},
                ["Carrier"] = {asc_ship_builder_template_name = "Carrier"}
            },
            CVars = {[0] = "asc_ship_builder_template_name"}
        })

        -- Ship configuration
        panel:AddControl("Header", {Text = "Ship Configuration"})

        panel:AddControl("TextBox", {
            Label = "Ship Name",
            Command = "asc_ship_builder_ship_name",
            MaxLength = 50
        })

        -- Options
        panel:AddControl("Header", {Text = "Options"})

        panel:AddControl("CheckBox", {
            Label = "Auto-link to Ship Core",
            Command = "asc_ship_builder_auto_link"
        })

        panel:AddControl("CheckBox", {
            Label = "Auto-wire Components",
            Command = "asc_ship_builder_auto_wire"
        })

        panel:AddControl("Slider", {
            Label = "Component Spacing",
            Command = "asc_ship_builder_component_spacing",
            Type = "Integer",
            Min = 50,
            Max = 500
        })

        -- Instructions
        panel:AddControl("Header", {Text = "Instructions"})
        panel:AddControl("Label", {Text = "Left Click: Place component or build template"})
        panel:AddControl("Label", {Text = "Right Click: Link entity to ship core"})
        panel:AddControl("Label", {Text = "Reload: Show ship status"})
    end

    -- Add language strings with safe access
    local success, result = pcall(function()
        if _G.language and _G.language.Add then
            _G.language.Add("tool.asc_ship_builder.name", "Ship Builder")
            _G.language.Add("tool.asc_ship_builder.desc", "Advanced ship construction and management tool")
            _G.language.Add("tool.asc_ship_builder.0", "Left click to place component, right click to link, reload for status")
            return true
        end
        return false
    end)

    if not success then
        print("[ASC Ship Builder] Warning: Could not add language strings")
    end
end
