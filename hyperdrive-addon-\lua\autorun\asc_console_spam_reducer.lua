--[[
    Advanced Space Combat - Console Spam Reducer v1.0.0
    
    Reduces console spam by controlling debug output, monitoring frequencies,
    and implementing smart logging systems across all ASC components.
]]

print("[Advanced Space Combat] Console Spam Reducer v1.0.0 - Loading...")

-- Initialize namespace
ASC = ASC or {}
ASC.SpamReducer = ASC.SpamReducer or {}

-- Configuration
ASC.SpamReducer.Config = {
    Enabled = true,
    LogLevel = 1, -- 0=Silent, 1=Errors Only, 2=Warnings, 3=Info, 4=Debug
    MaxMessagesPerSecond = 5,
    SuppressRepeatedMessages = true,
    SuppressDebugMessages = true,
    SuppressStatusUpdates = true,
    SuppressPerformanceMessages = true,
    SuppressInitializationMessages = false, -- Keep important startup messages
    
    -- System-specific controls
    Systems = {
        Debug = false,
        Performance = false,
        Memory = false,
        AI = false,
        Czech = false,
        CAP = false,
        Diagnostics = false,
        Monitoring = false,
        Integration = false
    }
}

-- State tracking
ASC.SpamReducer.State = {
    MessageCounts = {},
    LastMessages = {},
    SuppressedCount = 0,
    LastCleanup = 0
}

-- Original print function
local originalPrint = print

-- Message filtering patterns
ASC.SpamReducer.FilterPatterns = {
    -- Debug messages
    "^%[ASC.*%] Debug:",
    "^%[.*%] Debug:",
    "^%[.*%] DEBUG:",
    
    -- Status updates
    "^%[.*%] Status:",
    "^%[.*%] Update:",
    "^%[.*%] Checking",
    "^%[.*%] Running",
    
    -- Performance messages
    "^%[.*Performance.*%]",
    "^%[.*Memory.*%]",
    "^%[.*FPS.*%]",
    
    -- Monitoring messages
    "^%[.*Monitor.*%]",
    "^%[.*Diagnostic.*%]",
    "^%[.*Health.*%]",
    
    -- Repetitive system messages
    "initialized successfully",
    "loaded successfully",
    "system active",
    "timer active",
    "OK$",
    "ACTIVE$",
    "complete$"
}

-- Check if message should be filtered
function ASC.SpamReducer.ShouldFilter(message)
    if not ASC.SpamReducer.Config.Enabled then return false end
    
    -- Never filter error messages
    if string.find(message, "ERROR") or string.find(message, "Error") or string.find(message, "error") then
        return false
    end
    
    -- Never filter critical messages
    if string.find(message, "CRITICAL") or string.find(message, "Critical") or string.find(message, "critical") then
        return false
    end
    
    -- Filter based on patterns
    for _, pattern in ipairs(ASC.SpamReducer.FilterPatterns) do
        if string.find(message, pattern) then
            return true
        end
    end
    
    -- Filter system-specific messages
    for system, suppress in pairs(ASC.SpamReducer.Config.Systems) do
        if suppress and string.find(message, system) then
            return true
        end
    end
    
    return false
end

-- Check for repeated messages
function ASC.SpamReducer.IsRepeatedMessage(message)
    if not ASC.SpamReducer.Config.SuppressRepeatedMessages then return false end
    
    local currentTime = CurTime()
    local messageHash = util.CRC(message)
    
    -- Check if we've seen this message recently
    if ASC.SpamReducer.State.LastMessages[messageHash] then
        local lastTime = ASC.SpamReducer.State.LastMessages[messageHash]
        if currentTime - lastTime < 5 then -- Within 5 seconds
            return true
        end
    end
    
    ASC.SpamReducer.State.LastMessages[messageHash] = currentTime
    return false
end

-- Rate limiting
function ASC.SpamReducer.IsRateLimited()
    local currentTime = CurTime()
    local currentSecond = math.floor(currentTime)
    
    -- Reset counter for new second
    if not ASC.SpamReducer.State.MessageCounts[currentSecond] then
        ASC.SpamReducer.State.MessageCounts[currentSecond] = 0
        
        -- Cleanup old entries
        for second, count in pairs(ASC.SpamReducer.State.MessageCounts) do
            if currentSecond - second > 5 then
                ASC.SpamReducer.State.MessageCounts[second] = nil
            end
        end
    end
    
    ASC.SpamReducer.State.MessageCounts[currentSecond] = ASC.SpamReducer.State.MessageCounts[currentSecond] + 1
    
    return ASC.SpamReducer.State.MessageCounts[currentSecond] > ASC.SpamReducer.Config.MaxMessagesPerSecond
end

-- Enhanced print function
function print(...)
    local args = {...}
    local message = table.concat(args, " ")
    
    -- Check if message should be filtered
    if ASC.SpamReducer.ShouldFilter(message) then
        ASC.SpamReducer.State.SuppressedCount = ASC.SpamReducer.State.SuppressedCount + 1
        return
    end
    
    -- Check for repeated messages
    if ASC.SpamReducer.IsRepeatedMessage(message) then
        ASC.SpamReducer.State.SuppressedCount = ASC.SpamReducer.State.SuppressedCount + 1
        return
    end
    
    -- Check rate limiting
    if ASC.SpamReducer.IsRateLimited() then
        ASC.SpamReducer.State.SuppressedCount = ASC.SpamReducer.State.SuppressedCount + 1
        return
    end
    
    -- Allow message through
    originalPrint(...)
end

-- Disable debug modes in various systems
function ASC.SpamReducer.DisableDebugModes()
    -- Debug System
    if ASC.Debug and ASC.Debug.Config then
        ASC.Debug.Config.LogLevel = 1 -- Errors only
        ASC.Debug.Config.LogToConsole = false
    end
    
    -- Performance Monitor
    if ASC.PerformanceMonitor and ASC.PerformanceMonitor.Config then
        ASC.PerformanceMonitor.Config.ReportInterval = 300 -- 5 minutes instead of 1 minute
    end
    
    -- Memory Optimizer
    if ASC.MemoryOptimizer and ASC.MemoryOptimizer.Config then
        ASC.MemoryOptimizer.Config.DebugMode = false
    end
    
    -- Tool Restoration
    if ASC.ToolRestoration and ASC.ToolRestoration.Config then
        ASC.ToolRestoration.Config.DebugMode = false
    end
    
    -- Czech Localization
    if ASC.Czech and ASC.Czech.Config then
        ASC.Czech.Config.DebugMode = false
    end
    
    -- AI System
    if ASC.AI and ASC.AI.Config then
        ASC.AI.Config.DebugMode = false
    end
    
    -- CAP Integration
    if HYPERDRIVE and HYPERDRIVE.CAP and HYPERDRIVE.CAP.Config then
        HYPERDRIVE.CAP.Config.DebugMode = false
    end
    
    -- Diagnostics
    if ASC.Diagnostics and ASC.Diagnostics.Config then
        ASC.Diagnostics.Config.AutoRunOnStart = false
    end
end

-- Reduce monitoring frequencies
function ASC.SpamReducer.ReduceMonitoringFrequencies()
    -- Increase timer intervals to reduce frequency
    local timersToAdjust = {
        "ASC_PeriodicDiagnostics",
        "ASC_SystemMonitoring", 
        "ASC_PerformanceLogging",
        "HyperdriveRealTimeMonitoring",
        "ASC_MemoryOptimizer",
        "ASC_CriticalMonitor"
    }
    
    for _, timerName in ipairs(timersToAdjust) do
        if timer.Exists(timerName) then
            timer.Remove(timerName)
            
            -- Recreate with longer intervals
            if timerName == "ASC_PeriodicDiagnostics" then
                timer.Create(timerName, 300, 0, function() -- 5 minutes instead of frequent
                    if ASC.Diagnostics and ASC.Diagnostics.RunDiagnostics then
                        ASC.Diagnostics.RunDiagnostics()
                    end
                end)
            elseif timerName == "ASC_SystemMonitoring" then
                timer.Create(timerName, 60, 0, function() -- 1 minute instead of frequent
                    if ASC.SystemIntegration and ASC.SystemIntegration.MonitorSystems then
                        local success, err = pcall(ASC.SystemIntegration.MonitorSystems)
                        if not success then
                            originalPrint("[ASC] System monitoring error: " .. tostring(err))
                        end
                    end
                end)
            elseif timerName == "HyperdriveRealTimeMonitoring" then
                timer.Create(timerName, 5, 0, function() -- 5 seconds instead of frequent
                    if HYPERDRIVE and HYPERDRIVE.RealTime and HYPERDRIVE.RealTime.UpdateMonitoring then
                        HYPERDRIVE.RealTime.UpdateMonitoring()
                    end
                end)
            end
        end
    end
end

-- Console commands for spam control
concommand.Add("asc_spam_control", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then return end
    
    local action = args[1] or "status"
    
    if action == "enable" then
        ASC.SpamReducer.Config.Enabled = true
        ASC.SpamReducer.DisableDebugModes()
        ASC.SpamReducer.ReduceMonitoringFrequencies()
        originalPrint("[ASC] Console spam reduction ENABLED")
        
    elseif action == "disable" then
        ASC.SpamReducer.Config.Enabled = false
        originalPrint("[ASC] Console spam reduction DISABLED")
        
    elseif action == "level" and args[2] then
        local level = tonumber(args[2]) or 1
        ASC.SpamReducer.Config.LogLevel = math.Clamp(level, 0, 4)
        originalPrint("[ASC] Log level set to: " .. level)
        
    elseif action == "status" then
        originalPrint("[ASC] Console Spam Reducer Status:")
        originalPrint("  Enabled: " .. tostring(ASC.SpamReducer.Config.Enabled))
        originalPrint("  Log Level: " .. ASC.SpamReducer.Config.LogLevel)
        originalPrint("  Messages Suppressed: " .. ASC.SpamReducer.State.SuppressedCount)
        originalPrint("  Max Messages/Second: " .. ASC.SpamReducer.Config.MaxMessagesPerSecond)
        
    elseif action == "reset" then
        ASC.SpamReducer.State.SuppressedCount = 0
        ASC.SpamReducer.State.MessageCounts = {}
        ASC.SpamReducer.State.LastMessages = {}
        originalPrint("[ASC] Spam reducer state reset")
        
    else
        originalPrint("[ASC] Usage: asc_spam_control [enable|disable|level <0-4>|status|reset]")
    end
end)

-- Initialize spam reduction
function ASC.SpamReducer.Initialize()
    originalPrint("[ASC] Initializing Console Spam Reducer...")
    
    -- Apply spam reduction settings
    ASC.SpamReducer.DisableDebugModes()
    
    -- Delay frequency reduction to allow systems to load
    timer.Simple(10, function()
        ASC.SpamReducer.ReduceMonitoringFrequencies()
    end)
    
    -- Periodic cleanup
    timer.Create("ASC_SpamReducer_Cleanup", 60, 0, function()
        local currentTime = CurTime()
        
        -- Clean old message tracking
        for hash, time in pairs(ASC.SpamReducer.State.LastMessages) do
            if currentTime - time > 30 then
                ASC.SpamReducer.State.LastMessages[hash] = nil
            end
        end
        
        -- Report suppression stats occasionally
        if ASC.SpamReducer.State.SuppressedCount > 100 then
            originalPrint("[ASC] Suppressed " .. ASC.SpamReducer.State.SuppressedCount .. " console messages to reduce spam")
            ASC.SpamReducer.State.SuppressedCount = 0
        end
    end)
    
    originalPrint("[ASC] Console Spam Reducer initialized - Spam reduction active")
end

-- Auto-initialize
timer.Simple(1, function()
    ASC.SpamReducer.Initialize()
end)

print("[Advanced Space Combat] Console Spam Reducer v1.0.0 loaded successfully!")
