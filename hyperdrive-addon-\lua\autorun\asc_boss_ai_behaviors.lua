--[[
    Advanced Space Combat - AI Boss Behaviors v2.0.0
    
    Advanced AI behavior system for boss ships
    Features:
    - Intelligent combat patterns
    - Adaptive learning system
    - Multi-phase boss mechanics
    - Fleet command capabilities
    - Stealth and special abilities
]]

-- Ensure ASC namespace exists
ASC = ASC or {}
ASC.Boss = ASC.Boss or {}
ASC.Boss.AI = ASC.Boss.AI or {}

-- AI Behavior Functions
ASC.Boss.AI = {
    
    -- Update AI cooldowns
    UpdateAICooldowns = function(ai, deltaTime)
        -- Update ability cooldowns
        for ability, cooldown in pairs(ai.abilityCooldowns) do
            if cooldown > 0 then
                ai.abilityCooldowns[ability] = math.max(0, cooldown - deltaTime)
            end
        end
        
        -- Update behavior cooldowns
        for behavior, cooldown in pairs(ai.behaviorCooldowns) do
            if cooldown > 0 then
                ai.behaviorCooldowns[behavior] = math.max(0, cooldown - deltaTime)
            end
        end
    end,
    
    -- Update threat assessment
    UpdateThreatAssessment = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        if not IsValid(entity) then return end
        
        local threats = {}
        local bossPos = entity:GetPos()
        
        -- Find potential threats
        for _, ply in ipairs(player.GetAll()) do
            if IsValid(ply) and ply:Alive() then
                local distance = bossPos:Distance(ply:GetPos())
                if distance <= ai.engagementRange * 2 then
                    local threat = ASC.Boss.AI.CalculateThreatLevel(bossData, ply)
                    table.insert(threats, {entity = ply, threat = threat, distance = distance})
                end
            end
        end
        
        -- Find ship cores and other entities
        for _, ent in ipairs(ents.FindInSphere(bossPos, ai.engagementRange * 2)) do
            if IsValid(ent) and ent ~= entity then
                local class = ent:GetClass()
                if string.find(class, "ship_core") or string.find(class, "weapon") then
                    local distance = bossPos:Distance(ent:GetPos())
                    local threat = ASC.Boss.AI.CalculateThreatLevel(bossData, ent)
                    table.insert(threats, {entity = ent, threat = threat, distance = distance})
                end
            end
        end
        
        -- Sort threats by priority
        table.sort(threats, function(a, b)
            return a.threat > b.threat
        end)
        
        ai.threatAssessment = threats
        
        -- Update primary target
        if #threats > 0 then
            local newTarget = threats[1].entity
            if newTarget ~= ai.primaryTarget then
                ai.primaryTarget = newTarget
                table.insert(ai.targetHistory, {target = newTarget, time = CurTime()})
                
                -- Limit target history
                if #ai.targetHistory > 10 then
                    table.remove(ai.targetHistory, 1)
                end
            end
        else
            ai.primaryTarget = nil
        end
    end,
    
    -- Calculate threat level of an entity
    CalculateThreatLevel = function(bossData, target)
        if not IsValid(target) then return 0 end
        
        local threat = 0
        local distance = bossData.entity:GetPos():Distance(target:GetPos())
        
        -- Base threat by type
        if target:IsPlayer() then
            threat = 100
            -- Bonus for armed players
            local weapon = target:GetActiveWeapon()
            if IsValid(weapon) then
                threat = threat + 50
            end
        elseif string.find(target:GetClass(), "ship_core") then
            threat = 80
        elseif string.find(target:GetClass(), "weapon") then
            threat = 60
        else
            threat = 20
        end
        
        -- Distance factor (closer = more threatening)
        local maxRange = bossData.ai.engagementRange
        local distanceFactor = math.max(0, (maxRange - distance) / maxRange)
        threat = threat * (0.5 + distanceFactor * 0.5)
        
        -- Health factor (lower health targets are easier)
        if target.Health and target:Health() > 0 then
            local healthPercent = target:Health() / (target:GetMaxHealth() or target:Health())
            threat = threat * (0.7 + healthPercent * 0.3)
        end
        
        return threat
    end,
    
    -- Process AI decisions
    ProcessAIDecisions = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        if not IsValid(entity) then return end
        
        -- State machine
        if ai.currentState == "PATROL" then
            ASC.Boss.AI.ProcessPatrolState(bossData)
        elseif ai.currentState == "ENGAGE" then
            ASC.Boss.AI.ProcessEngageState(bossData)
        elseif ai.currentState == "RETREAT" then
            ASC.Boss.AI.ProcessRetreatState(bossData)
        elseif ai.currentState == "ABILITY" then
            ASC.Boss.AI.ProcessAbilityState(bossData)
        elseif ai.currentState == "PHASE_TRANSITION" then
            ASC.Boss.AI.ProcessPhaseTransitionState(bossData)
        end
        
        -- Check for state transitions
        ASC.Boss.AI.CheckStateTransitions(bossData)
    end,
    
    -- Process patrol state
    ProcessPatrolState = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        -- Look for targets
        if ai.primaryTarget and IsValid(ai.primaryTarget) then
            ai.currentState = "ENGAGE"
            ai.stateTimer = 0
            return
        end
        
        -- Patrol movement
        if not ai.movementTarget or entity:GetPos():Distance(ai.movementTarget) < 200 then
            -- Choose new patrol point
            local center = bossData.patrolCenter
            local radius = bossData.patrolRadius
            ai.movementTarget = center + VectorRand() * radius
        end
        
        -- Move towards patrol target
        ASC.Boss.AI.MoveTowards(bossData, ai.movementTarget, ai.movementSpeed * 0.5)
    end,
    
    -- Process engage state
    ProcessEngageState = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        if not ai.primaryTarget or not IsValid(ai.primaryTarget) then
            ai.currentState = "PATROL"
            ai.stateTimer = 0
            return
        end
        
        local distance = entity:GetPos():Distance(ai.primaryTarget:GetPos())
        
        -- Check if we should retreat
        local healthPercent = bossData.health / bossData.maxHealth
        if healthPercent < (ai.retreatThreshold or 0.2) then
            ai.currentState = "RETREAT"
            ai.stateTimer = 0
            return
        end
        
        -- Execute combat pattern
        ASC.Boss.AI.ExecuteCombatPattern(bossData)
        
        -- Use abilities
        ASC.Boss.AI.ConsiderAbilities(bossData)
        
        -- Combat movement
        ASC.Boss.AI.ExecuteCombatMovement(bossData)
    end,
    
    -- Process retreat state
    ProcessRetreatState = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        -- Move away from threats
        local retreatTarget = bossData.patrolCenter
        if ai.primaryTarget and IsValid(ai.primaryTarget) then
            local direction = (entity:GetPos() - ai.primaryTarget:GetPos()):GetNormalized()
            retreatTarget = entity:GetPos() + direction * 1000
        end
        
        ASC.Boss.AI.MoveTowards(bossData, retreatTarget, ai.movementSpeed)
        
        -- Check if we can re-engage
        local healthPercent = bossData.health / bossData.maxHealth
        if healthPercent > 0.5 then
            ai.currentState = "PATROL"
            ai.stateTimer = 0
        end
    end,
    
    -- Execute combat pattern
    ExecuteCombatPattern = function(bossData)
        local ai = bossData.ai
        local patterns = ai.combatPatterns
        
        if not patterns or #patterns == 0 then return end
        
        local currentPattern = patterns[ai.currentPattern] or patterns[1]
        
        if currentPattern == "frontal_assault" then
            ASC.Boss.AI.ExecuteFrontalAssault(bossData)
        elseif currentPattern == "flanking" then
            ASC.Boss.AI.ExecuteFlanking(bossData)
        elseif currentPattern == "hit_and_run" then
            ASC.Boss.AI.ExecuteHitAndRun(bossData)
        elseif currentPattern == "swarm_tactics" then
            ASC.Boss.AI.ExecuteSwarmTactics(bossData)
        elseif currentPattern == "energy_dominance" then
            ASC.Boss.AI.ExecuteEnergyDominance(bossData)
        elseif currentPattern == "stealth_ambush" then
            ASC.Boss.AI.ExecuteStealthAmbush(bossData)
        end
        
        -- Switch patterns periodically
        if ai.patternTimer > 30 then
            ai.currentPattern = (ai.currentPattern % #patterns) + 1
            ai.patternTimer = 0
        end
    end,
    
    -- Execute frontal assault pattern
    ExecuteFrontalAssault = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        if not ai.primaryTarget then return end
        
        -- Move directly towards target
        local targetPos = ai.primaryTarget:GetPos()
        ASC.Boss.AI.MoveTowards(bossData, targetPos, ai.movementSpeed)
        
        -- Fire all weapons
        ASC.Boss.AI.FireWeapons(bossData, ai.primaryTarget)
    end,
    
    -- Execute flanking pattern
    ExecuteFlanking = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        if not ai.primaryTarget then return end
        
        -- Calculate flanking position
        local targetPos = ai.primaryTarget:GetPos()
        local bossPos = entity:GetPos()
        local direction = (targetPos - bossPos):GetNormalized()
        local rightVector = direction:Cross(Vector(0, 0, 1)):GetNormalized()
        
        -- Alternate flanking sides
        local flankSide = (ai.patternTimer % 20 < 10) and 1 or -1
        local flankPos = targetPos + rightVector * flankSide * 800
        
        ASC.Boss.AI.MoveTowards(bossData, flankPos, ai.movementSpeed)
        ASC.Boss.AI.FireWeapons(bossData, ai.primaryTarget)
    end,
    
    -- Execute hit and run pattern
    ExecuteHitAndRun = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity
        
        if not ai.primaryTarget then return end
        
        local distance = entity:GetPos():Distance(ai.primaryTarget:GetPos())
        
        if distance > ai.engagementRange * 0.8 then
            -- Close in for attack
            ASC.Boss.AI.MoveTowards(bossData, ai.primaryTarget:GetPos(), ai.movementSpeed)
            ASC.Boss.AI.FireWeapons(bossData, ai.primaryTarget)
        else
            -- Retreat and fire
            local retreatDir = (entity:GetPos() - ai.primaryTarget:GetPos()):GetNormalized()
            local retreatPos = entity:GetPos() + retreatDir * 500
            ASC.Boss.AI.MoveTowards(bossData, retreatPos, ai.movementSpeed)
            ASC.Boss.AI.FireWeapons(bossData, ai.primaryTarget)
        end
    end,
    
    -- Move towards target position
    MoveTowards = function(bossData, targetPos, speed)
        local entity = bossData.entity
        if not IsValid(entity) then return end
        
        local currentPos = entity:GetPos()
        local direction = (targetPos - currentPos):GetNormalized()
        
        -- Apply movement
        local phys = entity:GetPhysicsObject()
        if IsValid(phys) then
            local velocity = direction * speed
            phys:SetVelocity(velocity)
            
            -- Face movement direction
            local angles = direction:Angle()
            entity:SetAngles(angles)
        end
    end,
    
    -- Fire weapons at target
    FireWeapons = function(bossData, target)
        if not IsValid(target) then return end
        
        local entity = bossData.entity
        local ai = bossData.ai
        
        -- Check if we have weapon system integration
        if ASC.Weapons and ASC.Weapons.Core then
            local shipID = tostring(entity:EntIndex())
            local weaponSystem = ASC.Weapons.Core.WeaponSystems[shipID]
            
            if weaponSystem then
                weaponSystem.currentTarget = target
                ASC.Weapons.Core.FireWeaponGroup(shipID, 1, target)
            end
        end
        
        -- Create weapon effects
        ASC.Boss.AI.CreateWeaponEffects(bossData, target)
    end,
    
    -- Create weapon effects
    CreateWeaponEffects = function(bossData, target)
        local entity = bossData.entity
        if not IsValid(entity) or not IsValid(target) then return end
        
        local startPos = entity:GetPos()
        local endPos = target:GetPos()
        
        -- Create beam effect
        local effectData = EffectData()
        effectData:SetStart(startPos)
        effectData:SetOrigin(endPos)
        effectData:SetEntity(entity)
        util.Effect("asc_boss_weapon_beam", effectData)
        
        -- Apply damage
        local damage = bossData.config.weapons and 100 or 50
        local dmgInfo = DamageInfo()
        dmgInfo:SetDamage(damage)
        dmgInfo:SetAttacker(entity)
        dmgInfo:SetInflictor(entity)
        dmgInfo:SetDamageType(DMG_ENERGYBEAM)
        
        target:TakeDamageInfo(dmgInfo)
        
        -- Sound effect
        entity:EmitSound("weapons/physcannon/energy_sing_loop4.wav", 80, 100)
    end,

    -- Consider using abilities
    ConsiderAbilities = function(bossData)
        local ai = bossData.ai
        local config = bossData.config

        if not config.abilities then return end

        for _, ability in ipairs(config.abilities) do
            if (ai.abilityCooldowns[ability] or 0) <= 0 then
                local shouldUse = ASC.Boss.AI.ShouldUseAbility(bossData, ability)
                if shouldUse then
                    ASC.Boss.AI.UseAbility(bossData, ability)
                    break -- Only use one ability per update
                end
            end
        end
    end,

    -- Check if ability should be used
    ShouldUseAbility = function(bossData, ability)
        local ai = bossData.ai
        local healthPercent = bossData.health / bossData.maxHealth

        if ability == "shield_boost" and healthPercent < 0.5 then
            return true
        elseif ability == "weapon_overcharge" and ai.primaryTarget then
            return true
        elseif ability == "tactical_retreat" and healthPercent < 0.3 then
            return true
        elseif ability == "fighter_launch" and ai.primaryTarget then
            return true
        elseif ability == "regeneration" and healthPercent < 0.6 then
            return true
        elseif ability == "cloak" and ai.type == "stealth_assassin" then
            return math.random() < 0.3
        end

        return false
    end,

    -- Use ability
    UseAbility = function(bossData, ability)
        local ai = bossData.ai
        local entity = bossData.entity

        if ability == "shield_boost" then
            bossData.shields = math.min(bossData.maxShields, bossData.shields + bossData.maxShields * 0.3)
            ai.abilityCooldowns[ability] = 30

        elseif ability == "weapon_overcharge" then
            ai.weaponAccuracy = ai.weaponAccuracy * 1.5
            ai.abilityCooldowns[ability] = 25

            timer.Simple(10, function()
                if ai then
                    ai.weaponAccuracy = ai.weaponAccuracy / 1.5
                end
            end)

        elseif ability == "tactical_retreat" then
            ai.currentState = "RETREAT"
            ai.stateTimer = 0
            ai.abilityCooldowns[ability] = 45

        elseif ability == "fighter_launch" then
            ASC.Boss.AI.LaunchFighters(bossData)
            ai.abilityCooldowns[ability] = 60

        elseif ability == "regeneration" then
            bossData.health = math.min(bossData.maxHealth, bossData.health + bossData.maxHealth * 0.2)
            ai.abilityCooldowns[ability] = 40

        elseif ability == "cloak" then
            ASC.Boss.AI.ActivateCloak(bossData)
            ai.abilityCooldowns[ability] = 60
        end

        -- Broadcast ability use
        if ASC.Boss.Core.BroadcastAbility then
            ASC.Boss.Core.BroadcastAbility(bossData, ability, "")
        end

        print("[Boss AI] " .. bossData.config.name .. " used ability: " .. ability)
    end,

    -- Launch fighters
    LaunchFighters = function(bossData)
        local entity = bossData.entity
        local ai = bossData.ai

        if not IsValid(entity) then return end

        local fighterCount = ai.swarmSize or 4

        for i = 1, fighterCount do
            local fighter = ents.Create("asc_ai_fighter")
            if IsValid(fighter) then
                local offset = VectorRand() * 200
                fighter:SetPos(entity:GetPos() + offset)
                fighter:SetAngles(entity:GetAngles())
                fighter:SetOwner(entity:GetOwner())
                fighter:Spawn()
                fighter:Activate()

                -- Set fighter AI target
                if ai.primaryTarget then
                    fighter:SetTarget(ai.primaryTarget)
                end

                table.insert(ai.commandedUnits, fighter)
            end
        end

        print("[Boss AI] Launched " .. fighterCount .. " fighters")
    end,

    -- Activate cloak
    ActivateCloak = function(bossData)
        local entity = bossData.entity
        local ai = bossData.ai

        if not IsValid(entity) then return end

        -- Make entity semi-transparent
        entity:SetRenderMode(RENDERMODE_TRANSALPHA)
        entity:SetColor(Color(255, 255, 255, 50))

        -- Reduce threat detection
        ai.stealthActive = true

        -- Deactivate cloak after duration
        local cloakDuration = ai.cloakDuration or 15
        timer.Simple(cloakDuration, function()
            if IsValid(entity) and ai then
                entity:SetRenderMode(RENDERMODE_NORMAL)
                entity:SetColor(Color(255, 255, 255, 255))
                ai.stealthActive = false
            end
        end)

        print("[Boss AI] Cloak activated for " .. cloakDuration .. " seconds")
    end,

    -- Execute combat movement
    ExecuteCombatMovement = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity

        if not IsValid(entity) or not ai.primaryTarget then return end

        local distance = entity:GetPos():Distance(ai.primaryTarget:GetPos())
        local preferredRange = ai.preferredRange or ai.engagementRange * 0.7

        -- Maintain preferred range
        if distance < preferredRange * 0.8 then
            -- Too close, back away
            local direction = (entity:GetPos() - ai.primaryTarget:GetPos()):GetNormalized()
            local moveTarget = entity:GetPos() + direction * 300
            ASC.Boss.AI.MoveTowards(bossData, moveTarget, ai.movementSpeed)

        elseif distance > preferredRange * 1.2 then
            -- Too far, close in
            ASC.Boss.AI.MoveTowards(bossData, ai.primaryTarget:GetPos(), ai.movementSpeed)

        else
            -- Good range, strafe
            ASC.Boss.AI.ExecuteStrafing(bossData)
        end

        -- Evasive maneuvers
        if CurTime() - ai.lastEvasion > 5 then
            ASC.Boss.AI.ExecuteEvasion(bossData)
            ai.lastEvasion = CurTime()
        end
    end,

    -- Execute strafing movement
    ExecuteStrafing = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity

        if not ai.primaryTarget then return end

        local targetPos = ai.primaryTarget:GetPos()
        local bossPos = entity:GetPos()
        local direction = (targetPos - bossPos):GetNormalized()
        local rightVector = direction:Cross(Vector(0, 0, 1)):GetNormalized()

        -- Strafe left or right
        local strafeDirection = (ai.stateTimer % 10 < 5) and 1 or -1
        local strafeTarget = bossPos + rightVector * strafeDirection * 400

        ASC.Boss.AI.MoveTowards(bossData, strafeTarget, ai.movementSpeed * 0.8)
    end,

    -- Execute evasive maneuvers
    ExecuteEvasion = function(bossData)
        local ai = bossData.ai
        local entity = bossData.entity

        if not IsValid(entity) then return end

        -- Random evasive movement
        local evasionVector = VectorRand() * 500
        evasionVector.z = evasionVector.z * 0.5 -- Reduce vertical movement

        local evasionTarget = entity:GetPos() + evasionVector
        ASC.Boss.AI.MoveTowards(bossData, evasionTarget, ai.movementSpeed * ai.agility)

        ai.evasionPattern = "active"

        timer.Simple(3, function()
            if ai then
                ai.evasionPattern = "none"
            end
        end)
    end,

    -- Check state transitions
    CheckStateTransitions = function(bossData)
        local ai = bossData.ai

        -- Implement state transition logic based on conditions
        -- This is called after processing current state

        -- Example transitions handled in individual state processors
        -- Additional complex transitions can be added here
    end,

    -- Update AI learning system
    UpdateAILearning = function(bossData)
        local ai = bossData.ai

        -- Simple learning: track successful vs failed actions
        if ai.primaryTarget and IsValid(ai.primaryTarget) then
            local distance = bossData.entity:GetPos():Distance(ai.primaryTarget:GetPos())

            -- Learn optimal engagement range
            if distance < ai.engagementRange then
                ai.experienceData.effectiveWeapons[ai.currentPattern] =
                    (ai.experienceData.effectiveWeapons[ai.currentPattern] or 0) + 1
            end
        end

        -- Adapt based on experience
        if ai.decisionsMade > 0 and ai.decisionsMade % 100 == 0 then
            ASC.Boss.AI.AdaptBehavior(bossData)
        end
    end,

    -- Adapt behavior based on experience
    AdaptBehavior = function(bossData)
        local ai = bossData.ai

        -- Increase accuracy if performing well
        if ai.correctDecisions / ai.decisionsMade > 0.7 then
            ai.weaponAccuracy = math.min(0.98, ai.weaponAccuracy + 0.05)
            ai.adaptationCount = ai.adaptationCount + 1
        end

        -- Adjust engagement range based on success
        local mostEffectivePattern = nil
        local maxEffectiveness = 0

        for pattern, effectiveness in pairs(ai.experienceData.effectiveWeapons) do
            if effectiveness > maxEffectiveness then
                maxEffectiveness = effectiveness
                mostEffectivePattern = pattern
            end
        end

        if mostEffectivePattern then
            -- Prioritize most effective pattern
            for i, pattern in ipairs(ai.combatPatterns) do
                if pattern == mostEffectivePattern then
                    ai.currentPattern = i
                    break
                end
            end
        end

        print("[Boss AI] " .. bossData.config.name .. " adapted behavior (Adaptations: " .. ai.adaptationCount .. ")")
    end
}

-- Integrate with main boss system
if ASC.Boss.Core then
    -- Add AI functions to main boss core
    ASC.Boss.Core.UpdateAICooldowns = ASC.Boss.AI.UpdateAICooldowns
    ASC.Boss.Core.UpdateThreatAssessment = ASC.Boss.AI.UpdateThreatAssessment
    ASC.Boss.Core.ProcessAIDecisions = ASC.Boss.AI.ProcessAIDecisions
    ASC.Boss.Core.ExecuteAIBehavior = ASC.Boss.AI.ExecuteCombatPattern
    ASC.Boss.Core.UpdateAILearning = ASC.Boss.AI.UpdateAILearning
    ASC.Boss.Core.CheckPhaseTransition = function(bossData) end -- Placeholder
    ASC.Boss.Core.UpdateFleetCommand = function(bossData) end -- Placeholder

    print("[Advanced Space Combat] AI Boss Behaviors v2.0.0 loaded and integrated")
end
