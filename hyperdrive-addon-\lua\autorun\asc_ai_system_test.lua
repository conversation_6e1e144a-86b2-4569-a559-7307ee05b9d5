-- Advanced Space Combat - ARIA-4 AI System Test & Verification
-- This file tests the AI system functionality and provides diagnostic information

if not ASC then ASC = {} end
ASC.AITest = {}

-- Test configuration
ASC.AITest.Config = {
    RunOnStartup = true,
    LogResults = true,
    TestDelay = 3 -- Delay before running tests to ensure AI system is loaded
}

-- Test results storage
ASC.AITest.Results = {
    TestsPassed = 0,
    TestsFailed = 0,
    Errors = {},
    StartTime = 0,
    EndTime = 0
}

-- Test 1: Basic AI System Initialization
function ASC.AITest.TestAIInitialization()
    print("[AI Test] Testing AI system initialization...")
    
    local tests = {
        {name = "ASC.AI exists", test = function() return ASC.AI ~= nil end},
        {name = "ProcessQuery function exists", test = function() return ASC.AI.ProcessQuery ~= nil end},
        {name = "AnalyzeQuery function exists", test = function() return ASC.AI.AnalyzeQuery ~= nil end},
        {name = "FindPlayerShipCore function exists", test = function() return ASC.AI.FindPlayerShipCore ~= nil end},
        {name = "ConversationHistory initialized", test = function() return ASC.AI.ConversationHistory ~= nil end},
        {name = "UserProfiles initialized", test = function() return ASC.AI.UserProfiles ~= nil end},
        {name = "NLP module exists", test = function() return ASC.AI.NLP ~= nil end},
        {name = "EmotionalIntelligence module exists", test = function() return ASC.AI.EmotionalIntelligence ~= nil end}
    }
    
    local passed = 0
    for _, test in ipairs(tests) do
        local success, result = pcall(test.test)
        if success and result then
            print("[AI Test] ✓ " .. test.name)
            passed = passed + 1
        else
            print("[AI Test] ✗ " .. test.name .. " - " .. tostring(result))
            table.insert(ASC.AITest.Results.Errors, test.name .. " failed")
        end
    end
    
    if passed == #tests then
        ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
        return true
    else
        ASC.AITest.Results.TestsFailed = ASC.AITest.Results.TestsFailed + 1
        return false
    end
end

-- Test 2: Chat Command Processing
function ASC.AITest.TestChatCommands()
    print("[AI Test] Testing chat command processing...")
    
    local success, result = pcall(function()
        -- Test if chat hook is registered
        local hooks = hook.GetTable()
        if hooks.PlayerSay and hooks.PlayerSay["ASC_AI_Chat"] then
            return true
        end
        return false
    end)
    
    if success and result then
        print("[AI Test] ✓ Chat command hook registered")
        ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
        return true
    else
        print("[AI Test] ✗ Chat command hook not found")
        table.insert(ASC.AITest.Results.Errors, "Chat command hook missing")
        ASC.AITest.Results.TestsFailed = ASC.AITest.Results.TestsFailed + 1
        return false
    end
end

-- Test 3: NLP Functions
function ASC.AITest.TestNLPFunctions()
    print("[AI Test] Testing NLP functions...")
    
    local tests = {
        {
            name = "Intent Analysis",
            test = function()
                local intent, confidence = ASC.AI.NLP.AnalyzeIntent("help me with my ship")
                return intent == "help" and confidence > 0
            end
        },
        {
            name = "Sentiment Analysis", 
            test = function()
                local sentiment, score = ASC.AI.NLP.AnalyzeSentiment("please help me")
                return sentiment == "positive" and score > 0
            end
        },
        {
            name = "Context Extraction",
            test = function()
                local contexts = ASC.AI.NLP.ExtractContext("ship weapon status")
                return contexts.ship == 1 and contexts.weapons == 1
            end
        }
    }
    
    local passed = 0
    for _, test in ipairs(tests) do
        local success, result = pcall(test.test)
        if success and result then
            print("[AI Test] ✓ " .. test.name)
            passed = passed + 1
        else
            print("[AI Test] ✗ " .. test.name .. " - " .. tostring(result))
            table.insert(ASC.AITest.Results.Errors, test.name .. " failed")
        end
    end
    
    if passed == #tests then
        ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
        return true
    else
        ASC.AITest.Results.TestsFailed = ASC.AITest.Results.TestsFailed + 1
        return false
    end
end

-- Test 4: AI Response Generation
function ASC.AITest.TestResponseGeneration()
    print("[AI Test] Testing AI response generation...")
    
    -- Create a mock player for testing
    local mockPlayer = {
        SteamID = function() return "STEAM_TEST" end,
        ChatPrint = function(msg) print("[Mock Player] " .. msg) end,
        IsValid = function() return true end
    }
    
    local success, result = pcall(function()
        if ASC.AI.AnalyzeQuery then
            local analysis = ASC.AI.AnalyzeQuery(mockPlayer, "aria help")
            return analysis ~= nil and analysis.intent ~= nil
        end
        return false
    end)
    
    if success and result then
        print("[AI Test] ✓ Response generation working")
        ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
        return true
    else
        print("[AI Test] ✗ Response generation failed - " .. tostring(result))
        table.insert(ASC.AITest.Results.Errors, "Response generation failed")
        ASC.AITest.Results.TestsFailed = ASC.AITest.Results.TestsFailed + 1
        return false
    end
end

-- Test 5: Console Commands
function ASC.AITest.TestConsoleCommands()
    print("[AI Test] Testing console commands...")
    
    local commands = {"aria_test", "aria_reset", "aria_debug", "asc_ai_status"}
    local passed = 0
    
    for _, cmd in ipairs(commands) do
        if concommand.GetTable()[cmd] then
            print("[AI Test] ✓ Console command '" .. cmd .. "' registered")
            passed = passed + 1
        else
            print("[AI Test] ✗ Console command '" .. cmd .. "' missing")
            table.insert(ASC.AITest.Results.Errors, "Console command " .. cmd .. " missing")
        end
    end
    
    if passed == #commands then
        ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
        return true
    else
        ASC.AITest.Results.TestsFailed = ASC.AITest.Results.TestsFailed + 1
        return false
    end
end

-- Test 6: Integration Systems
function ASC.AITest.TestIntegrationSystems()
    print("[AI Test] Testing integration systems...")
    
    local integrations = {
        {name = "ULib", check = function() return ASC.AI.ULib and ASC.AI.ULib.IsAvailable end},
        {name = "ULX", check = function() return ASC.AI.ULX and ASC.AI.ULX.IsAvailable end},
        {name = "CPPI", check = function() return ASC.AI.CPPI and ASC.AI.CPPI.IsAvailable end},
        {name = "CAP", check = function() return ASC.AI.CAP and ASC.AI.CAP.IsAvailable end},
        {name = "Wiremod", check = function() return ASC.AI.Wiremod and ASC.AI.Wiremod.IsAvailable end}
    }
    
    local available = 0
    for _, integration in ipairs(integrations) do
        local success, result = pcall(integration.check)
        if success and result then
            local isAvailable = result()
            if isAvailable then
                print("[AI Test] ✓ " .. integration.name .. " integration available")
                available = available + 1
            else
                print("[AI Test] ○ " .. integration.name .. " integration not available (addon not installed)")
            end
        else
            print("[AI Test] ✗ " .. integration.name .. " integration check failed")
        end
    end
    
    print("[AI Test] Integration summary: " .. available .. "/" .. #integrations .. " systems available")
    ASC.AITest.Results.TestsPassed = ASC.AITest.Results.TestsPassed + 1
    return true
end

-- Run all tests
function ASC.AITest.RunAllTests()
    print("[AI Test] =====================================")
    print("[AI Test] ARIA-4 AI System Diagnostic Test")
    print("[AI Test] =====================================")
    
    ASC.AITest.Results.StartTime = CurTime()
    ASC.AITest.Results.TestsPassed = 0
    ASC.AITest.Results.TestsFailed = 0
    ASC.AITest.Results.Errors = {}
    
    -- Run tests
    ASC.AITest.TestAIInitialization()
    ASC.AITest.TestChatCommands()
    ASC.AITest.TestNLPFunctions()
    ASC.AITest.TestResponseGeneration()
    ASC.AITest.TestConsoleCommands()
    ASC.AITest.TestIntegrationSystems()
    
    ASC.AITest.Results.EndTime = CurTime()
    local testDuration = ASC.AITest.Results.EndTime - ASC.AITest.Results.StartTime
    
    -- Show results
    local totalTests = ASC.AITest.Results.TestsPassed + ASC.AITest.Results.TestsFailed
    local successRate = totalTests > 0 and (ASC.AITest.Results.TestsPassed / totalTests * 100) or 0
    
    print("[AI Test] =====================================")
    print("[AI Test] TEST RESULTS")
    print("[AI Test] Tests Passed: " .. ASC.AITest.Results.TestsPassed)
    print("[AI Test] Tests Failed: " .. ASC.AITest.Results.TestsFailed)
    print("[AI Test] Success Rate: " .. math.Round(successRate, 1) .. "%")
    print("[AI Test] Test Duration: " .. math.Round(testDuration, 2) .. " seconds")
    
    if #ASC.AITest.Results.Errors > 0 then
        print("[AI Test] Errors Found:")
        for _, error in ipairs(ASC.AITest.Results.Errors) do
            print("[AI Test] • " .. error)
        end
    end
    
    print("[AI Test] =====================================")
    
    if ASC.AITest.Results.TestsFailed == 0 then
        print("[AI Test] 🎉 ALL TESTS PASSED - AI System is functioning correctly!")
        print("[AI Test] 💡 Try: 'aria help', 'aria status', or 'aria ship status'")
    else
        print("[AI Test] ⚠️ Some tests failed - AI System may have issues")
        print("[AI Test] 🔧 Check the errors above and verify AI system files")
    end
    
    return ASC.AITest.Results.TestsFailed == 0
end

-- Console command to run tests
concommand.Add("asc_test_ai", function(ply, cmd, args)
    local success = ASC.AITest.RunAllTests()
    local msg = "[AI Test] AI System Test " .. (success and "PASSED" or "FAILED")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Auto-run tests if enabled
if ASC.AITest.Config.RunOnStartup then
    timer.Simple(ASC.AITest.Config.TestDelay, function()
        print("[AI Test] Auto-running AI system tests...")
        ASC.AITest.RunAllTests()
    end)
end

print("[Advanced Space Combat] AI System Test & Verification loaded - Use 'asc_test_ai' to run tests")
