--[[
    Advanced Space Combat - Base Weapon Client
]]

include("shared.lua")

-- Initialize client
function ENT:Initialize()
    self.GlowIntensity = 0
    self.HeatGlow = 0
    self.LastParticleTime = 0
    self.ParticleEmitter = nil
end

-- Draw weapon
function ENT:Draw()
    self:DrawModel()
    
    -- Draw status indicators
    self:DrawStatusIndicators()
    
    -- Draw heat effects
    if self:GetHeat() > 0.5 then
        self:DrawHeatEffects()
    end
    
    -- Draw targeting indicator
    if IsValid(self:GetTarget()) then
        self:DrawTargetingLine()
    end
end

-- Draw status indicators
function ENT:DrawStatusIndicators()
    local pos = self:GetPos() + self:GetUp() * 30
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang.Forward, 90)
    ang:RotateAroundAxis(ang.Right, 90)
    
    local distance = LocalPlayer():GetPos():Distance(pos)
    if distance > 1000 then return end
    
    local scale = math.Clamp(1 - (distance / 1000), 0.1, 1)
    
    cam.Start3D2D(pos, ang, scale * 0.1)
        local y = 0
        
        -- Weapon name
        draw.SimpleText(self:GetWeaponName(), "DermaDefaultBold", 0, y, Color(255, 255, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        y = y + 20
        
        -- Status indicators
        if self:GetOverheated() then
            draw.SimpleText("OVERHEATED", "DermaDefault", 0, y, Color(255, 100, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        elseif self:GetAutoFire() then
            draw.SimpleText("AUTO-FIRE", "DermaDefault", 0, y, Color(100, 255, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        elseif self:GetActive() then
            draw.SimpleText("READY", "DermaDefault", 0, y, Color(100, 200, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        else
            draw.SimpleText("OFFLINE", "DermaDefault", 0, y, Color(150, 150, 150), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        y = y + 15
        
        -- Heat bar
        local heatPercent = self:GetHeat()
        if heatPercent > 0 then
            local barWidth = 100
            local barHeight = 8
            
            -- Background
            surface.SetDrawColor(50, 50, 50, 200)
            surface.DrawRect(-barWidth/2, y, barWidth, barHeight)
            
            -- Heat fill
            local heatColor = Color(
                255 * heatPercent,
                255 * (1 - heatPercent),
                0
            )
            surface.SetDrawColor(heatColor.r, heatColor.g, heatColor.b, 200)
            surface.DrawRect(-barWidth/2, y, barWidth * heatPercent, barHeight)
            
            -- Border
            surface.SetDrawColor(255, 255, 255, 100)
            surface.DrawOutlinedRect(-barWidth/2, y, barWidth, barHeight)
            
            y = y + 12
        end
        
        -- Ammo indicator
        local ammo = self:GetAmmo()
        if ammo >= 0 then
            draw.SimpleText("AMMO: " .. math.floor(ammo), "DermaDefault", 0, y, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        
    cam.End3D2D()
end

-- Draw heat effects
function ENT:DrawHeatEffects()
    local heatLevel = self:GetHeat()
    
    -- Create particle emitter if needed
    if not self.ParticleEmitter then
        self.ParticleEmitter = ParticleEmitter(self:GetPos())
    end
    
    -- Emit heat particles
    if CurTime() - self.LastParticleTime > 0.1 and heatLevel > 0.3 then
        self.LastParticleTime = CurTime()
        
        for i = 1, math.ceil(heatLevel * 3) do
            local particle = self.ParticleEmitter:Add("effects/fire_cloud1", self:GetPos() + VectorRand() * 20)
            if particle then
                particle:SetVelocity(Vector(0, 0, 50) + VectorRand() * 20)
                particle:SetLifeTime(0)
                particle:SetDieTime(1 + math.random() * 2)
                particle:SetStartAlpha(100 * heatLevel)
                particle:SetEndAlpha(0)
                particle:SetStartSize(5)
                particle:SetEndSize(15)
                particle:SetColor(255, 100 + 155 * (1 - heatLevel), 0)
            end
        end
    end
    
    -- Dynamic light for heat
    if heatLevel > 0.5 then
        local dlight = DynamicLight(self:EntIndex())
        if dlight then
            dlight.pos = self:GetPos()
            dlight.r = 255
            dlight.g = 100 + 155 * (1 - heatLevel)
            dlight.b = 0
            dlight.brightness = heatLevel * 2
            dlight.decay = 1000
            dlight.size = 100 * heatLevel
            dlight.dietime = CurTime() + 0.1
        end
    end
end

-- Draw targeting line
function ENT:DrawTargetingLine()
    local target = self:GetTarget()
    if not IsValid(target) then return end
    
    local startPos = self:GetPos()
    local endPos = target:GetPos()
    
    -- Check if we should draw the line (distance and line of sight)
    local distance = startPos:Distance(endPos)
    if distance > 3000 then return end
    
    -- Draw targeting beam
    render.SetMaterial(Material("cable/rope"))
    render.DrawBeam(startPos, endPos, 2, 0, 1, Color(255, 100, 100, 100))
    
    -- Draw target indicator
    local targetPos = endPos + Vector(0, 0, 20)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang.Forward, 90)
    ang:RotateAroundAxis(ang.Right, 90)
    
    cam.Start3D2D(targetPos, ang, 0.2)
        draw.SimpleText("TARGET", "DermaDefault", 0, 0, Color(255, 100, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    cam.End3D2D()
end

-- Think function for client effects
function ENT:Think()
    -- Update glow effects
    local heatLevel = self:GetHeat()
    self.HeatGlow = Lerp(FrameTime() * 5, self.HeatGlow, heatLevel)
    
    -- Update particle effects
    if self:GetOverheated() and math.random() < 0.5 then
        self:CreateOverheatSparks()
    end
end

-- Create overheat spark effects
function ENT:CreateOverheatSparks()
    if not self.ParticleEmitter then
        self.ParticleEmitter = ParticleEmitter(self:GetPos())
    end
    
    local particle = self.ParticleEmitter:Add("effects/spark", self:GetPos() + VectorRand() * 15)
    if particle then
        particle:SetVelocity(VectorRand() * 100)
        particle:SetLifeTime(0)
        particle:SetDieTime(0.5 + math.random() * 0.5)
        particle:SetStartAlpha(255)
        particle:SetEndAlpha(0)
        particle:SetStartSize(2)
        particle:SetEndSize(0)
        particle:SetColor(255, 255, 100)
    end
end

-- Cleanup
function ENT:OnRemove()
    if self.ParticleEmitter then
        self.ParticleEmitter:Finish()
        self.ParticleEmitter = nil
    end
end
