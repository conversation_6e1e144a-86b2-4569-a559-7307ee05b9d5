--[[
    Advanced Space Combat - Localization Integration Fix Test v1.0.0
    
    Specific test for the localization integration language API error fix.
    This addresses the "attempt to index global 'language' (a nil value)" error.
]]

-- Initialize Localization Integration Fix Test namespace
ASC = ASC or {}
ASC.LocalizationIntegrationFixTest = ASC.LocalizationIntegrationFixTest or {}

-- Test the specific localization integration fix
function ASC.LocalizationIntegrationFixTest.TestLanguageAPIFix()
    print("[Localization Fix Test] Testing localization integration language API fix...")
    
    local testResults = {
        passed = 0,
        failed = 0,
        errors = {},
        warnings = {}
    }
    
    -- Test 1: Check if localization integration system exists
    if not ASC.LocalizationIntegration then
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "Localization integration system not found")
        return false, testResults
    end
    
    testResults.passed = testResults.passed + 1
    print("[Localization Fix Test] ✓ Localization integration system found")
    
    -- Test 2: Test the Validate function that was causing the language.GetPhrase error
    if ASC.LocalizationIntegration.Validate then
        local success, result = pcall(function()
            return ASC.LocalizationIntegration.Validate()
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[Localization Fix Test] ✓ Validate function runs without language API errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "Validate function still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "Validate function not found")
    end
    
    -- Test 3: Test the AutoSetCzechForPlayers function that was calling GetConVar
    if ASC.LocalizationIntegration.AutoSetCzechForPlayers then
        local success, result = pcall(function()
            return ASC.LocalizationIntegration.AutoSetCzechForPlayers()
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[Localization Fix Test] ✓ AutoSetCzechForPlayers runs without ConVar errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "AutoSetCzechForPlayers still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "AutoSetCzechForPlayers function not found")
    end
    
    -- Test 4: Test the full integration process
    if ASC.LocalizationIntegration.FullIntegration then
        local success, result = pcall(function()
            return ASC.LocalizationIntegration.FullIntegration()
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[Localization Fix Test] ✓ Full integration process runs without errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "Full integration still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "FullIntegration function not found")
    end
    
    -- Test 5: Test language API access methods directly
    local languageAPITests = {
        {
            name = "Direct language.GetPhrase access",
            test = function()
                if CLIENT then
                    local success, result = pcall(function()
                        return language and language.GetPhrase and language.GetPhrase("test") or "safe"
                    end)
                    return success
                else
                    return true -- Skip on server
                end
            end
        },
        {
            name = "GetConVar access",
            test = function()
                local success, result = pcall(function()
                    local convar = GetConVar("gmod_language")
                    return convar and convar:GetString() or "safe"
                end)
                return success
            end
        },
        {
            name = "Alternative language detection",
            test = function()
                -- Test our alternative methods
                if ASC.GMod and ASC.GMod.Localization and ASC.GMod.Localization.Core then
                    return ASC.GMod.Localization.Core.CurrentLanguage ~= nil
                end
                return true -- No alternative system, that's ok
            end
        }
    }
    
    for _, apiTest in ipairs(languageAPITests) do
        local success = apiTest.test()
        if success then
            testResults.passed = testResults.passed + 1
            print("[Localization Fix Test] ✓ " .. apiTest.name .. " - safe")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, apiTest.name .. " - still has issues")
        end
    end
    
    local allPassed = testResults.failed == 0
    print("[Localization Fix Test] Localization Integration Fix: " .. (allPassed and "✓ WORKING" or "✗ FAILED"))
    
    return allPassed, testResults
end

-- Run comprehensive localization integration fix test
function ASC.LocalizationIntegrationFixTest.RunAllTests()
    print("[Localization Fix Test] ==========================================")
    print("[Localization Fix Test] Localization Integration Fix Test")
    print("[Localization Fix Test] ==========================================")
    
    local startTime = CurTime()
    
    -- Run the main test
    local success, results = ASC.LocalizationIntegrationFixTest.TestLanguageAPIFix()
    
    local testTime = CurTime() - startTime
    
    -- Print summary
    print("[Localization Fix Test] ==========================================")
    print("[Localization Fix Test] TEST SUMMARY")
    print("[Localization Fix Test] ==========================================")
    print("[Localization Fix Test] Overall Status: " .. (success and "✅ ALL TESTS PASSED" or "❌ SOME TESTS FAILED"))
    print("[Localization Fix Test] Tests Passed: " .. results.passed)
    print("[Localization Fix Test] Tests Failed: " .. results.failed)
    print("[Localization Fix Test] Test Duration: " .. string.format("%.3fs", testTime))
    
    if #results.errors > 0 then
        print("[Localization Fix Test] ERRORS:")
        for _, error in ipairs(results.errors) do
            print("[Localization Fix Test]   - " .. error)
        end
    end
    
    if #results.warnings > 0 then
        print("[Localization Fix Test] WARNINGS:")
        for _, warning in ipairs(results.warnings) do
            print("[Localization Fix Test]   - " .. warning)
        end
    end
    
    if success then
        print("[Localization Fix Test] 🎉 Localization integration language API error has been FIXED!")
        print("[Localization Fix Test] The system now safely handles language API access.")
    else
        print("[Localization Fix Test] ❌ Localization integration fix needs more work.")
    end
    
    print("[Localization Fix Test] ==========================================")
    
    return success
end

-- Console command for testing the localization integration fix
concommand.Add("asc_test_localization_integration_fix", function(ply, cmd, args)
    local success = ASC.LocalizationIntegrationFixTest.RunAllTests()
    
    local msg = "[Localization Fix Test] " .. (success and "✅ Localization integration fix is working!" or "❌ Localization integration fix has issues")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Quick status command
concommand.Add("asc_localization_fix_status", function(ply, cmd, args)
    local msg = "[Localization Fix Test] Testing localization integration fix..."
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
    
    -- Run quick test
    local success, results = ASC.LocalizationIntegrationFixTest.TestLanguageAPIFix()
    
    local statusMsg = "[Localization Fix Test] Status: " .. (success and "✅ WORKING" or "❌ FAILED") .. 
                     " (" .. results.passed .. " passed, " .. results.failed .. " failed)"
    
    if IsValid(ply) then
        ply:ChatPrint(statusMsg)
    else
        print(statusMsg)
    end
end)

-- Auto-test on load
timer.Simple(4.5, function()
    print("[Localization Fix Test] Running automatic localization integration fix test...")
    local success = ASC.LocalizationIntegrationFixTest.RunAllTests()
    
    if success then
        print("[Localization Fix Test] ✅ Auto-test PASSED - Localization integration fix is working!")
    else
        print("[Localization Fix Test] ❌ Auto-test FAILED - Localization integration fix needs attention!")
    end
end)

print("[Advanced Space Combat] Localization Integration Fix Test v1.0.0 Loaded")
