Advanced Space Combat - Model Assets

This directory contains 3D models for Advanced Space Combat entities.

Required Models:
- ship_core.mdl - Central ship management hub
- hyperdrive_engine.mdl - Standard propulsion system
- hyperdrive_master_engine.mdl - Advanced propulsion system
- asc_pulse_cannon.mdl - Energy weapon system
- asc_railgun.mdl - Kinetic weapon system
- asc_plasma_cannon.mdl - Plasma weapon system
- asc_beam_weapon.mdl - Continuous beam weapon
- asc_torpedo_launcher.mdl - Guided missile system
- asc_shield_generator.mdl - Defensive shield system
- asc_flight_console.mdl - Ship control interface
- asc_docking_pad.mdl - Landing and docking system
- asc_shuttle.mdl - Small transport vessel

Model Requirements:
- Source Engine compatible (.mdl, .vvd, .vtx files)
- Proper collision models for physics
- Attachment points for weapon systems
- LOD (Level of Detail) models for performance
- Proper material assignments

Note: These models should be created using professional 3D modeling software
and compiled using Source Engine tools. Placeholder models are used until
proper assets are created.

For model creation guidelines, see the Advanced Space Combat documentation.
