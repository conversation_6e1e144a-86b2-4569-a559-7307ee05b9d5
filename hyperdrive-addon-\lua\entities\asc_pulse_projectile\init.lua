--[[
    Advanced Space Combat - Enhanced Pulse Projectile

    High-velocity energy projectile for pulse cannons
    Features:
    - Energy-based damage with shield bonuses
    - Homing capabilities
    - Penetration system
    - Advanced visual effects
    - Smart collision detection
]]

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    -- Enhanced entity setup
    self:SetModel("models/effects/teleporttrail.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetCollisionGroup(COLLISION_GROUP_PROJECTILE)

    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(1)
        phys:EnableGravity(false)
        phys:SetDragCoefficient(0)
        phys:SetBuoyancyRatio(0)
    end

    -- Enhanced projectile properties
    self.Damage = 85
    self.Speed = 2500
    self.LifeTime = 3.0
    self.HomingStrength = 0.1
    self.PenetrationPower = 2
    self.EnergyType = "pulse"
    self.ShieldPenetration = 0.8 -- 80% shield penetration

    -- State variables
    self.StartTime = CurTime()
    self.Target = nil
    self.Weapon = nil
    self.HasHit = false
    self.PenetratedTargets = {}

    -- Effects timing
    self.NextTrailTime = 0
    self.NextSparkTime = 0

    -- Networking
    self:SetNWFloat("Damage", self.Damage)
    self:SetNWFloat("Speed", self.Speed)
    self:SetNWString("EnergyType", self.EnergyType)
    self:SetNWBool("HasTarget", false)

    -- Enhanced trail effect
    util.SpriteTrail(self, 0, Color(100, 150, 255), false, 20, 2, 0.8, 1/(20+2)*0.8, "trails/laser.vmt")

    print("[ASC Pulse Projectile] Enhanced projectile initialized")
end

-- Set projectile properties
function ENT:SetDamage(damage)
    self.Damage = damage
    self:SetNWFloat("Damage", damage)
end

function ENT:SetSpeed(speed)
    self.Speed = speed
    self:SetNWFloat("Speed", speed)

    -- Update velocity
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        local direction = phys:GetVelocity():GetNormalized()
        phys:SetVelocity(direction * speed)
    end
end

function ENT:SetLifetime(lifetime)
    self.LifeTime = lifetime
end

function ENT:SetTarget(target)
    self.Target = target
    self:SetNWEntity("Target", target)
    self:SetNWBool("HasTarget", IsValid(target))
end

function ENT:SetWeapon(weapon)
    self.Weapon = weapon
    self:SetNWEntity("Weapon", weapon)
end

-- Enhanced think function
function ENT:Think()
    local currentTime = CurTime()

    -- Check lifetime
    if currentTime - self.StartTime > self.LifeTime then
        self:CreateExpirationEffect()
        self:Remove()
        return
    end

    -- Homing behavior
    if IsValid(self.Target) and self.HomingStrength > 0 then
        self:UpdateHoming()
    end

    -- Update effects
    if currentTime > self.NextSparkTime then
        self:CreateSparkEffect()
        self.NextSparkTime = currentTime + 0.1
    end

    self:NextThink(currentTime + 0.02) -- 50 FPS
    return true
end

-- Update homing behavior
function ENT:UpdateHoming()
    if not IsValid(self.Target) then return end

    local phys = self:GetPhysicsObject()
    if not IsValid(phys) then return end

    local currentVel = phys:GetVelocity()
    local currentDir = currentVel:GetNormalized()
    local targetDir = (self.Target:GetPos() - self:GetPos()):GetNormalized()

    -- Calculate new direction with homing
    local newDir = LerpVector(self.HomingStrength, currentDir, targetDir)
    newDir:Normalize()

    -- Apply new velocity
    phys:SetVelocity(newDir * self.Speed)

    -- Update angles
    self:SetAngles(newDir:Angle())
end

-- Create spark effect
function ENT:CreateSparkEffect()
    if math.random() < 0.3 then
        local effectData = EffectData()
        effectData:SetOrigin(self:GetPos() + VectorRand() * 5)
        effectData:SetMagnitude(0.5)
        util.Effect("ElectricSpark", effectData)
    end
end

-- Create expiration effect
function ENT:CreateExpirationEffect()
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos())
    effectData:SetMagnitude(1)
    util.Effect("asc_pulse_dissipate", effectData)
end

-- Enhanced collision detection
function ENT:PhysicsCollide(data, phys)
    if self.HasHit then return end

    local hitEntity = data.HitEntity
    local hitPos = data.HitPos
    local hitNormal = data.HitNormal

    -- Ignore certain entities
    if not IsValid(hitEntity) then
        self:CreateImpactEffect(hitPos, hitNormal, nil)
        self:Remove()
        return
    end

    -- Ignore owner and weapon
    if hitEntity == self:GetOwner() or hitEntity == self.Weapon then
        return
    end

    -- Ignore friendly entities
    local owner = self:GetOwner()
    if IsValid(owner) and hitEntity:GetOwner() == owner then
        return
    end

    -- Check if already penetrated this target
    if self.PenetratedTargets[hitEntity] then
        return
    end

    self.HasHit = true

    -- Apply damage
    self:DealDamage(hitEntity, hitPos, hitNormal)

    -- Create impact effects
    self:CreateImpactEffect(hitPos, hitNormal, hitEntity)

    -- Check for penetration
    if self.PenetrationPower > 0 then
        self.PenetrationPower = self.PenetrationPower - 1
        self.Damage = self.Damage * 0.7 -- Reduce damage after penetration
        self.PenetratedTargets[hitEntity] = true
        self.HasHit = false

        -- Continue with reduced velocity
        local phys = self:GetPhysicsObject()
        if IsValid(phys) then
            local vel = phys:GetVelocity()
            phys:SetVelocity(vel * 0.8)
        end
    else
        self:Remove()
    end
end

-- Deal damage to hit entity
function ENT:DealDamage(hitEntity, hitPos, hitNormal)
    local damage = self.Damage

    -- Bonus damage against shields and energy systems
    local class = hitEntity:GetClass()
    if string.find(class, "shield") then
        damage = damage * 1.5 -- 50% bonus against shields
    elseif string.find(class, "energy") or string.find(class, "power") then
        damage = damage * 1.3 -- 30% bonus against energy systems
    elseif string.find(class, "ship_core") then
        damage = damage * 1.2 -- 20% bonus against ship cores
    end

    -- Create damage info
    local dmgInfo = DamageInfo()
    dmgInfo:SetDamage(damage)
    dmgInfo:SetAttacker(self:GetOwner() or self)
    dmgInfo:SetInflictor(self.Weapon or self)
    dmgInfo:SetDamagePosition(hitPos)
    dmgInfo:SetDamageForce(self:GetVelocity():GetNormalized() * damage * 15)
    dmgInfo:SetDamageType(DMG_ENERGYBEAM)

    -- Apply damage
    hitEntity:TakeDamageInfo(dmgInfo)

    -- Update weapon statistics
    if IsValid(self.Weapon) then
        self.Weapon.TotalDamage = (self.Weapon.TotalDamage or 0) + damage
        self.Weapon.TotalHits = (self.Weapon.TotalHits or 0) + 1
    end

    print("[ASC Pulse Projectile] Dealt " .. damage .. " damage to " .. tostring(hitEntity))
end

-- Create impact effects
function ENT:CreateImpactEffect(hitPos, hitNormal, hitEntity)
    -- Main impact effect
    local effectData = EffectData()
    effectData:SetOrigin(hitPos)
    effectData:SetNormal(hitNormal)
    effectData:SetMagnitude(self.Damage)
    effectData:SetScale(1.2)
    util.Effect("asc_pulse_impact", effectData)

    -- Secondary spark effects
    for i = 1, 4 do
        timer.Simple(i * 0.05, function()
            local sparkPos = hitPos + hitNormal * 5 + VectorRand() * 15
            local effectData = EffectData()
            effectData:SetOrigin(sparkPos)
            effectData:SetMagnitude(0.8)
            util.Effect("ElectricSpark", effectData)
        end)
    end

    -- Enhanced sound effects
    local impactSounds = {
        "ambient/energy/spark1.wav",
        "ambient/energy/spark2.wav",
        "ambient/energy/spark3.wav",
        "ambient/energy/spark4.wav",
        "ambient/energy/spark5.wav",
        "ambient/energy/spark6.wav"
    }

    sound.Play(impactSounds[math.random(#impactSounds)], hitPos, 75, math.random(90, 110))

    -- Additional sound for different materials
    if hitEntity then
        local material = hitEntity:GetMaterialType()
        if material == MAT_METAL then
            sound.Play("physics/metal/metal_solid_impact_bullet" .. math.random(1, 4) .. ".wav", hitPos, 60, math.random(80, 120))
        elseif material == MAT_CONCRETE then
            sound.Play("physics/concrete/concrete_impact_bullet" .. math.random(1, 4) .. ".wav", hitPos, 60, math.random(80, 120))
        end
    end

    -- Dynamic light
    local dlight = DynamicLight(self:EntIndex())
    if dlight then
        dlight.pos = hitPos
        dlight.r = 100
        dlight.g = 150
        dlight.b = 255
        dlight.brightness = 3
        dlight.decay = 800
        dlight.size = 120
        dlight.dietime = CurTime() + 0.6
    end

    -- Screen shake for nearby players
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:GetPos():Distance(hitPos) < 300 then
            local distance = ply:GetPos():Distance(hitPos)
            local intensity = math.Clamp(1 - (distance / 300), 0, 1)
            ply:ViewPunch(Angle(math.random(-1, 1) * intensity, math.random(-1, 1) * intensity, 0))
        end
    end
end

-- Touch function for additional collision detection
function ENT:Touch(ent)
    if self.HasHit then return end
    if not IsValid(ent) then return end
    if ent == self:GetOwner() or ent == self.Weapon then return end
    if ent:GetClass() == "worldspawn" then return end

    -- Trigger collision
    local data = {
        HitEntity = ent,
        HitPos = self:GetPos(),
        HitNormal = -self:GetVelocity():GetNormalized()
    }

    self:PhysicsCollide(data, self:GetPhysicsObject())
end

-- Get projectile status
function ENT:GetProjectileStatus()
    return {
        damage = self.Damage,
        speed = self.Speed,
        lifetime = self.LifeTime,
        age = CurTime() - self.StartTime,
        target = self.Target,
        weapon = self.Weapon,
        energyType = self.EnergyType,
        penetrationPower = self.PenetrationPower,
        hasHit = self.HasHit,
        shieldPenetration = self.ShieldPenetration
    }
end
