-- Advanced Space Combat - Simple Working Localization System v2.0.0
-- <PERSON>s <PERSON>'s Mod's built-in localization system properly

print("[Advanced Space Combat] Simple Localization System v2.0.0 Loading...")

-- Initialize localization namespace
ASC = ASC or {}
ASC.Localization = ASC.Localization or {}

-- Simple configuration
ASC.Localization.Config = {
    Enabled = true,
    DefaultLanguage = "en",
    SupportedLanguages = {"en", "cs"},
    LogTranslations = false
}

-- Current language state
ASC.Localization.CurrentLanguage = "en"

-- Get current language
function ASC.Localization.GetCurrentLanguage()
    if CLIENT then
        local gmodLang = GetConVar("gmod_language")
        if gmodLang then
            local lang = gmodLang:GetString()
            if lang == "czech" or lang == "cs" then
                return "cs"
            end
        end
    end
    return ASC.Localization.CurrentLanguage
end

-- Set language
function ASC.Localization.SetLanguage(language)
    if language == "cs" or language == "czech" then
        ASC.Localization.CurrentLanguage = "cs"
        print("[ASC Localization] Language set to Czech")
        return true
    else
        ASC.Localization.CurrentLanguage = "en"
        print("[ASC Localization] Language set to English")
        return true
    end
end

-- Get localized text - the main function everyone should use
function ASC.Localization.GetText(key, fallback)
    if not ASC.Localization.Config.Enabled then
        return fallback or key
    end

    -- Try to get translation from GMod's language system
    if language and language.GetPhrase then
        local translation = language.GetPhrase(key)
        if translation and translation ~= key then
            return translation
        end
    end

    -- Fallback to provided fallback or key
    return fallback or key
end

-- Global helper function for easy access throughout the addon
-- This is the main function all entities, tools, and UI should use
function ASC_GetText(key, fallback)
    -- Try the main localization system first
    if ASC and ASC.Localization and ASC.Localization.GetText then
        return ASC.Localization.GetText(key, fallback)
    end

    -- Try Czech system as backup
    if ASC and ASC.Czech and ASC.Czech.GetText then
        return ASC.Czech.GetText(key, fallback)
    end

    -- Try direct GMod language system
    if language and language.GetPhrase then
        local translation = language.GetPhrase(key)
        if translation and translation ~= key then
            return translation
        end
    end

    -- Final fallback
    return fallback or key
end

-- Convenience function for entity localization
function ASC_GetEntityText(entityClass, textType, fallback)
    local key = "asc.entity." .. entityClass .. "." .. textType
    return ASC_GetText(key, fallback)
end

-- Convenience function for tool localization
function ASC_GetToolText(toolName, textType, fallback)
    local key = "tool." .. toolName .. "." .. textType
    return ASC_GetText(key, fallback)
end

-- Convenience function for UI localization
function ASC_GetUIText(uiElement, fallback)
    local key = "asc.ui." .. uiElement
    return ASC_GetText(key, fallback)
end

-- Load translations from properties files
function ASC.Localization.LoadTranslations()
    print("[ASC Localization] Loading translations...")

    for _, lang in ipairs(ASC.Localization.Config.SupportedLanguages) do
        local filePath = "resource/localization/" .. lang .. "/advanced_space_combat.properties"

        if file.Exists(filePath, "GAME") then
            print("[ASC Localization] Loading " .. lang .. " translations from " .. filePath)

            local content = file.Read(filePath, "GAME")
            if content then
                ASC.Localization.ParsePropertiesFile(content, lang)
            end
        else
            print("[ASC Localization] Properties file not found: " .. filePath)
        end
    end
end

-- Parse properties file and add translations
function ASC.Localization.ParsePropertiesFile(content, languageCode)
    if not content or content == "" then return end

    local lines = string.Split(content, "\n")
    local addedCount = 0

    for _, line in ipairs(lines) do
        line = string.Trim(line:gsub("\r", ""))

        -- Skip empty lines and comments
        if line ~= "" and not string.StartWith(line, "#") then
            local key, value = line:match("^([^=]+)=(.*)$")
            if key and value then
                key = string.Trim(key)
                value = string.Trim(value)

                -- Add to GMod's language system
                if language and language.Add then
                    language.Add(key, value)
                    addedCount = addedCount + 1
                end
            end
        end
    end

    print("[ASC Localization] Added " .. addedCount .. " translations for " .. languageCode)
end

-- Initialize the system
function ASC.Localization.Initialize()
    print("[ASC Localization] Initializing...")

    -- Load translations
    ASC.Localization.LoadTranslations()

    -- Auto-detect Czech if needed
    if CLIENT then
        timer.Simple(1, function()
            if ASC.Czech and ASC.Czech.Config and ASC.Czech.Config.Enabled then
                ASC.Localization.SetLanguage("cs")
            end
        end)
    end

    print("[ASC Localization] Initialization complete")
end

-- Console command to change language
concommand.Add("asc_lang", function(ply, cmd, args)
    if not args[1] then
        local currentLang = ASC.Localization.GetCurrentLanguage()
        local msg = "[ASC Localization] Current language: " .. currentLang
        if IsValid(ply) then
            ply:ChatPrint(msg)
        else
            print(msg)
        end
        return
    end

    local newLang = args[1]
    ASC.Localization.SetLanguage(newLang)

    local msg = "[ASC Localization] Language changed to: " .. newLang
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Test command to verify localization is working
concommand.Add("asc_test_localization", function(ply, cmd, args)
    local msg = "[ASC Localization Test]"
    if IsValid(ply) then
        ply:ChatPrint(msg)
        ply:ChatPrint("Testing localization system...")

        -- Test some basic translations
        local testKeys = {
            "asc.addon.name",
            "asc.hyperdrive.name",
            "asc.weapons.name",
            "asc.shields.name"
        }

        for _, key in ipairs(testKeys) do
            local translation = ASC.Localization.GetText(key, "NOT_FOUND")
            ply:ChatPrint(key .. " = " .. translation)
        end

        ply:ChatPrint("Test complete. If you see 'NOT_FOUND', localization is not working.")
    else
        print(msg)
        print("Testing localization system...")

        local testKeys = {
            "asc.addon.name",
            "asc.hyperdrive.name",
            "asc.weapons.name",
            "asc.shields.name"
        }

        for _, key in ipairs(testKeys) do
            local translation = ASC.Localization.GetText(key, "NOT_FOUND")
            print(key .. " = " .. translation)
        end

        print("Test complete. If you see 'NOT_FOUND', localization is not working.")
    end
end)

-- Initialize on load
if CLIENT then
    timer.Simple(0.5, function()
        ASC.Localization.Initialize()
    end)
else
    ASC.Localization.Initialize()
end

-- Test command to verify the fixed localization system
concommand.Add("asc_test_localization_fixed", function(ply, cmd, args)
    local function TestKey(key, fallback)
        local result = ASC_GetText(key, fallback)
        local msg = "[Localization Test] " .. key .. " = " .. result
        if IsValid(ply) then
            ply:ChatPrint(msg)
        else
            print(msg)
        end
        return result
    end

    local msg = "[Advanced Space Combat] Testing Fixed Localization System"
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end

    -- Test key entity names
    TestKey("asc.entity.asc_ship_core.name", "ASC Ship Core")
    TestKey("asc.entity.hyperdrive_master_engine.name", "Ultimate Hyperdrive Engine")
    TestKey("asc.entity.hyperdrive_computer.name", "ASC Enhanced Navigation Computer")

    -- Test UI elements
    TestKey("asc.ui.close", "Close")
    TestKey("asc.ui.save", "Save")
    TestKey("asc.addon.loading", "Loading...")

    -- Test categories
    TestKey("asc.category.hyperdrive", "Hyperdrive")
    TestKey("asc.category.advanced_space_combat", "Advanced Space Combat")

    -- Test tool names
    TestKey("tool.asc_main_tool.name", "ASC Main Tool")
    TestKey("tool.asc_ship_core_tool.name", "ASC Ship Core Tool v6.0.0")

    local finalMsg = "[Advanced Space Combat] Localization test complete!"
    if IsValid(ply) then
        ply:ChatPrint(finalMsg)
    else
        print(finalMsg)
    end
end)

print("[Advanced Space Combat] Simple Localization System v2.0.0 loaded")



