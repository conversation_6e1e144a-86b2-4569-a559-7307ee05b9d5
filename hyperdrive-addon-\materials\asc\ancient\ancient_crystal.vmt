"VertexLitGeneric"
{
	"$basetexture" "asc/ancient/ancient_crystal_diffuse"
	"$bumpmap" "asc/ancient/ancient_crystal_normal"
	"$envmap" "env_cubemap"
	"$envmaptint" "[0.5 0.7 1.0]"
	"$envmapfresnel" "1"
	"$translucent" "1"
	"$alpha" "0.8"
	"$phong" "1"
	"$phongexponent" "50"
	"$phongboost" "2.0"
	"$phongtint" "[0.6 0.8 1.0]"
	"$phongfresnelranges" "[0.1 0.5 1.0]"
	"$rimlight" "1"
	"$rimlightexponent" "2"
	"$rimlightboost" "1.0"
	"$selfillum" "1"
	"$selfillummask" "asc/ancient/ancient_crystal_glow"
	"$selfillumtint" "[0.3 0.5 0.9]"
	"$refract" "1"
	"$refractamount" "0.1"
}
