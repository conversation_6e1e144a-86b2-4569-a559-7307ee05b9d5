-- Ship Core Compatibility Entity - Redirects to ASC Ship Core
-- This provides backward compatibility for old "ship_core" class

ENT.Type = "anim"
ENT.Base = "base_gmodentity"

ENT.PrintName = "Ship Core (Legacy)"
ENT.Author = "Advanced Space Combat Team"
ENT.Contact = ""
ENT.Purpose = "Legacy ship core - automatically redirects to ASC Ship Core"
ENT.Instructions = "This is a compatibility entity. Use ASC Ship Core instead."

ENT.Spawnable = false  -- Don't show in spawn menu
ENT.AdminSpawnable = false
ENT.Category = "Advanced Space Combat - Ship Cores"

-- Compatibility flag
ENT.IsLegacyShipCore = true
ENT.RedirectsToASC = true

-- Network variables for compatibility
function ENT:SetupDataTables()
    -- Basic compatibility network vars
    self:NetworkVar("Bool", 0, "IsRedirecting")
    
    if SERVER then
        self:SetIsRedirecting(true)
    end
end

-- Compatibility functions that match ASC ship core
function ENT:IsShipCore()
    return true
end

function ENT:IsValidShipCoreClass()
    return true
end

-- State compatibility
ENT.States = {
    INACTIVE = 0,
    ACTIVE = 1,
    INVALID = 2,
    CRITICAL = 3,
    EMERGENCY = 4
}

function ENT:GetState()
    return self.States.ACTIVE  -- Always return active for compatibility
end

function ENT:GetStateName()
    return "REDIRECTING"
end

function ENT:GetStateColor()
    return Color(100, 150, 255)  -- Blue for redirecting
end
