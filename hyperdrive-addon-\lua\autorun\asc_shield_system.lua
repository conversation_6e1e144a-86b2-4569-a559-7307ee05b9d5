--[[
    Advanced Space Combat - Enhanced Shield System v4.0.0

    Comprehensive shield management system with CAP integration
    Features adaptive shielding, tactical AI integration, multi-layer protection,
    and advanced shield mechanics with real-time damage absorption
]]

-- Initialize Shield System namespace
ASC = ASC or {}
ASC.Shields = ASC.Shields or {}
ASC.Shields.Core = ASC.Shields.Core or {}

-- Enhanced Shield System Configuration
ASC.Shields.Config = {
    -- Core Settings
    Enabled = true,
    UpdateRate = 0.05, -- 20 FPS for smooth updates
    MaxShieldsPerShip = 1,

    -- Built-in Shield System (No generators needed)
    BuiltinShields = true,
    RequireGenerators = false,
    AutoActivate = true,
    SmartRecharge = true,
    AdaptiveShielding = true,

    -- Enhanced Shield Types (Direct ship core integration)
    ShieldTypes = {
        BASIC = {
            name = "Basic Shield",
            strength = 1200,
            rechargeRate = 60,
            energyCost = 25,
            coverage = 0.85,
            rechargeDelay = 3.0,
            builtin = true,
            color = Color(100, 150, 255, 80),
            efficiency = 0.8
        },
        ADVANCED = {
            name = "Advanced Shield",
            strength = 2500,
            rechargeRate = 125,
            energyCost = 40,
            coverage = 0.92,
            rechargeDelay = 2.5,
            builtin = true,
            color = Color(150, 200, 255, 100),
            efficiency = 0.9
        },
        TACTICAL = {
            name = "Tactical Shield",
            strength = 4000,
            rechargeRate = 200,
            energyCost = 60,
            coverage = 0.97,
            rechargeDelay = 2.0,
            builtin = true,
            color = Color(200, 150, 255, 120),
            efficiency = 0.95
        },
        FORTRESS = {
            name = "Fortress Shield",
            strength = 7000,
            rechargeRate = 300,
            energyCost = 90,
            coverage = 0.99,
            rechargeDelay = 1.5,
            builtin = true,
            color = Color(255, 200, 100, 140),
            efficiency = 0.98
        },
        CAP_ENHANCED = {
            name = "CAP Enhanced Shield",
            strength = 6000,
            rechargeRate = 250,
            energyCost = 75,
            coverage = 1.0,
            rechargeDelay = 1.0,
            builtin = true,
            capIntegration = true,
            color = Color(100, 255, 200, 160),
            efficiency = 1.0
        },
        EXPERIMENTAL = {
            name = "Experimental Shield",
            strength = 10000,
            rechargeRate = 400,
            energyCost = 120,
            coverage = 1.0,
            rechargeDelay = 0.5,
            builtin = true,
            adaptiveRecharge = true,
            color = Color(255, 100, 255, 180),
            efficiency = 1.1
        }
    },

    -- Enhanced Visual Effects
    ShieldEffects = {
        HitEffect = "asc_shield_impact",
        RechargeEffect = "asc_shield_recharge",
        OverloadEffect = "asc_shield_overload",
        ActivationEffect = "asc_shield_activate",
        DeactivationEffect = "asc_shield_deactivate",
        BubbleEffect = "asc_shield_bubble",

        -- Enhanced Sound System
        Sounds = {
            activation = "ambient/energy/force_field_loop1.wav",
            deactivation = "ambient/energy/weld2.wav",
            hit = "ambient/energy/spark",
            overload = "ambient/explosions/explode_7.wav",
            recharge = "ambient/energy/weld1.wav",
            lowPower = "ambient/alarms/klaxon1.wav"
        }
    },

    -- Advanced Integration Settings
    CAPIntegration = true,
    TacticalAIIntegration = true,
    AutoRecharge = true,
    AdaptiveShielding = true,
    SmartTargeting = true,
    DamageAnalysis = true,

    -- Performance Settings
    MaxBubbleRadius = 500,
    MinBubbleRadius = 100,
    BubbleScaling = true,
    EffectQuality = "HIGH", -- HIGH, MEDIUM, LOW
    ParticleLimit = 100
}

-- Enhanced Shield System Core
ASC.Shields.Core = {
    -- Active shield systems
    ActiveShields = {},

    -- Shield statistics and performance tracking
    ShieldStats = {},

    -- Shield update timer
    UpdateTimer = nil,

    -- Initialize enhanced shield system for a ship
    Initialize = function(shipCore, shieldType)
        if not IsValid(shipCore) then
            return false, "Invalid ship core"
        end

        local shipID = tostring(shipCore:EntIndex())
        shieldType = shieldType or "ADVANCED"

        local config = ASC.Shields.Config.ShieldTypes[shieldType]
        if not config then
            return false, "Invalid shield type: " .. tostring(shieldType)
        end

        -- Calculate shield radius based on ship size
        local shipRadius = ASC.Shields.Core.CalculateShipRadius(shipCore)
        local shieldRadius = math.max(ASC.Shields.Config.MinBubbleRadius,
                                    math.min(ASC.Shields.Config.MaxBubbleRadius, shipRadius * 1.5))

        -- Create enhanced shield system with built-in shields
        ASC.Shields.Core.ActiveShields[shipID] = {
            shipCore = shipCore,
            shieldType = shieldType,
            config = config,

            -- Enhanced shield status
            currentStrength = config.strength,
            maxStrength = config.strength,
            recharging = false,
            lastDamageTime = 0,
            active = true,
            builtin = true,
            efficiency = config.efficiency or 1.0,

            -- Advanced properties
            shieldRadius = shieldRadius,
            shieldColor = config.color or Color(100, 150, 255, 100),
            coverage = config.coverage or 1.0,
            rechargeDelay = config.rechargeDelay or 3.0,

            -- Optional external components (for bonus effects)
            generators = {},
            capShields = {},
            bubbleEntity = nil,

            -- Enhanced performance tracking
            damageAbsorbed = 0,
            damageBlocked = 0,
            rechargeCount = 0,
            overloadCount = 0,
            activationCount = 0,
            totalUptime = 0,
            lastActivation = CurTime(),

            -- Tactical integration
            threatLevel = 0,
            adaptiveMode = config.adaptiveRecharge or false,
            prioritySectors = {},
            damageHistory = {},

            -- Real-time status
            energyConsumption = 0,
            lastUpdate = CurTime(),
            updateRate = ASC.Shields.Config.UpdateRate,

            -- Visual effects
            effectEntities = {},
            soundChannels = {},
            lastEffectTime = 0
        }

        -- Initialize shield statistics
        ASC.Shields.Core.ShieldStats[shipID] = {
            totalDamageAbsorbed = 0,
            totalRecharges = 0,
            totalOverloads = 0,
            averageEfficiency = 1.0,
            uptime = 0,
            activations = 0
        }

        -- Detect additional shield generators for bonus strength
        ASC.Shields.Core.DetectBonusGenerators(shipID)

        -- Detect CAP shields for integration
        ASC.Shields.Core.DetectCAPShields(shipID)

        -- Create shield bubble effect
        ASC.Shields.Core.CreateShieldBubble(shipID)

        -- Activate built-in shields
        ASC.Shields.Core.ActivateBuiltinShields(shipID)

        -- Start update timer if not already running
        ASC.Shields.Core.StartUpdateTimer()

        print("[ASC Shield System] Enhanced shield system initialized for ship " .. shipID .. " - Type: " .. shieldType)
        print("[ASC Shield System] Shield strength: " .. config.strength .. ", Radius: " .. shieldRadius .. ", Coverage: " .. (config.coverage * 100) .. "%")

        return true, "Enhanced shield system initialized"
    end,

    -- Calculate ship radius for shield sizing
    CalculateShipRadius = function(shipCore)
        if not IsValid(shipCore) then return 150 end

        local radius = 150 -- Default radius

        -- Try to get ship entities for size calculation
        if shipCore.ship and shipCore.ship.entities then
            local minPos, maxPos = Vector(0, 0, 0), Vector(0, 0, 0)
            local entityCount = 0

            for _, ent in ipairs(shipCore.ship.entities) do
                if IsValid(ent) then
                    local pos = ent:GetPos()
                    if entityCount == 0 then
                        minPos, maxPos = pos, pos
                    else
                        minPos.x = math.min(minPos.x, pos.x)
                        minPos.y = math.min(minPos.y, pos.y)
                        minPos.z = math.min(minPos.z, pos.z)
                        maxPos.x = math.max(maxPos.x, pos.x)
                        maxPos.y = math.max(maxPos.y, pos.y)
                        maxPos.z = math.max(maxPos.z, pos.z)
                    end
                    entityCount = entityCount + 1
                end
            end

            if entityCount > 0 then
                local size = maxPos - minPos
                radius = math.max(size:Length() / 2, 150)
            end
        end

        return radius
    end,

    -- Create shield bubble effect
    CreateShieldBubble = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then return end

        -- Remove existing bubble
        if IsValid(shield.bubbleEntity) then
            shield.bubbleEntity:Remove()
        end

        -- Create new shield bubble
        local bubble = ents.Create("asc_shield_bubble")
        if IsValid(bubble) then
            bubble:SetPos(shield.shipCore:GetPos())
            bubble:SetOwner(shield.shipCore:GetOwner())
            bubble:SetParent(shield.shipCore)
            bubble:Spawn()
            bubble:Activate()

            -- Configure bubble
            bubble:SetShieldRadius(shield.shieldRadius)
            bubble:SetShieldColor(shield.shieldColor)
            bubble:SetShieldStrength(shield.currentStrength)
            bubble:SetMaxShieldStrength(shield.maxStrength)
            bubble:SetShieldGenerator(shield.shipCore)

            shield.bubbleEntity = bubble
            table.insert(shield.effectEntities, bubble)

            print("[ASC Shield System] Created shield bubble for ship " .. shipID .. " with radius " .. shield.shieldRadius)
        else
            print("[ASC Shield System] Failed to create shield bubble entity")
        end
    end,

    -- Start shield update timer
    StartUpdateTimer = function()
        if ASC.Shields.Core.UpdateTimer then return end

        ASC.Shields.Core.UpdateTimer = timer.Create("ASC_ShieldUpdate", ASC.Shields.Config.UpdateRate, 0, function()
            ASC.Shields.Core.UpdateAllShields()
        end)

        print("[ASC Shield System] Started shield update timer")
    end,

    -- Update all active shields
    UpdateAllShields = function()
        local currentTime = CurTime()

        for shipID, shield in pairs(ASC.Shields.Core.ActiveShields) do
            if IsValid(shield.shipCore) then
                ASC.Shields.Core.UpdateShield(shipID, currentTime)
            else
                -- Clean up invalid shields
                ASC.Shields.Core.RemoveShield(shipID)
            end
        end
    end,

    -- Update individual shield
    UpdateShield = function(shipID, currentTime)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return end

        local deltaTime = currentTime - shield.lastUpdate
        shield.lastUpdate = currentTime

        -- Update shield recharging
        if shield.recharging and shield.currentStrength < shield.maxStrength then
            local rechargeAmount = shield.config.rechargeRate * deltaTime * shield.efficiency
            shield.currentStrength = math.min(shield.maxStrength, shield.currentStrength + rechargeAmount)

            -- Update bubble entity
            if IsValid(shield.bubbleEntity) then
                shield.bubbleEntity:SetShieldStrength(shield.currentStrength)
            end

            -- Check if fully recharged
            if shield.currentStrength >= shield.maxStrength then
                shield.recharging = false
                shield.rechargeCount = shield.rechargeCount + 1
                ASC.Shields.Core.PlayShieldSound(shipID, "recharge")
            end
        end

        -- Check recharge delay
        if not shield.recharging and shield.currentStrength < shield.maxStrength then
            if currentTime - shield.lastDamageTime >= shield.rechargeDelay then
                shield.recharging = true
                ASC.Shields.Core.CreateRechargeEffect(shipID)
            end
        end

        -- Update energy consumption
        if shield.active and shield.currentStrength > 0 then
            local energyCost = shield.config.energyCost * deltaTime
            shield.energyConsumption = energyCost

            -- Consume energy from ship core
            if IsValid(shield.shipCore) and shield.shipCore.ConsumeEnergy then
                if not shield.shipCore:ConsumeEnergy(energyCost) then
                    -- Not enough energy - reduce shield efficiency
                    shield.efficiency = math.max(0.1, shield.efficiency - 0.1)
                end
            end
        end

        -- Update statistics
        shield.totalUptime = shield.totalUptime + deltaTime

        -- Update networking
        ASC.Shields.Core.UpdateNetworking(shipID)
    end,

    -- Handle shield damage
    AbsorbDamage = function(shipID, damage, damagePos, damageType)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not shield.active or shield.currentStrength <= 0 then
            return damage -- No shield protection
        end

        -- Calculate damage absorption based on coverage
        local coverage = shield.coverage or 1.0
        local absorbedDamage = damage * coverage * shield.efficiency

        -- Apply damage type modifiers
        if damageType then
            if damageType == DMG_ENERGYBEAM then
                absorbedDamage = absorbedDamage * 0.8 -- Energy weapons are more effective
            elseif damageType == DMG_PLASMA then
                absorbedDamage = absorbedDamage * 0.7 -- Plasma is very effective
            elseif damageType == DMG_BULLET then
                absorbedDamage = absorbedDamage * 1.2 -- Kinetic weapons less effective
            end
        end

        -- Absorb damage
        local actualAbsorbed = math.min(absorbedDamage, shield.currentStrength)
        shield.currentStrength = shield.currentStrength - actualAbsorbed
        shield.lastDamageTime = CurTime()
        shield.recharging = false

        -- Update statistics
        shield.damageAbsorbed = shield.damageAbsorbed + actualAbsorbed
        shield.damageBlocked = shield.damageBlocked + 1

        -- Add to damage history for adaptive shielding
        table.insert(shield.damageHistory, {
            time = CurTime(),
            damage = actualAbsorbed,
            position = damagePos,
            type = damageType
        })

        -- Limit damage history size
        if #shield.damageHistory > 50 then
            table.remove(shield.damageHistory, 1)
        end

        -- Create impact effects
        ASC.Shields.Core.CreateImpactEffect(shipID, damagePos, actualAbsorbed)

        -- Update bubble entity
        if IsValid(shield.bubbleEntity) then
            shield.bubbleEntity:SetShieldStrength(shield.currentStrength)
            shield.bubbleEntity:OnDamage(actualAbsorbed, damagePos)
        end

        -- Check for shield overload
        if shield.currentStrength <= 0 then
            ASC.Shields.Core.OverloadShield(shipID)
        end

        -- Return remaining damage
        local remainingDamage = damage - actualAbsorbed
        return math.max(0, remainingDamage)
    end,

    -- Handle shield overload
    OverloadShield = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return end

        shield.overloadCount = shield.overloadCount + 1
        shield.recharging = false

        -- Create overload effects
        ASC.Shields.Core.CreateOverloadEffect(shipID)
        ASC.Shields.Core.PlayShieldSound(shipID, "overload")

        -- Temporary efficiency reduction
        shield.efficiency = math.max(0.5, shield.efficiency - 0.2)

        -- Restore efficiency over time
        timer.Simple(10, function()
            if shield then
                shield.efficiency = math.min(1.0, shield.efficiency + 0.2)
            end
        end)

        print("[ASC Shield System] Shield overloaded for ship " .. shipID)
    end,

    -- Create impact effect
    CreateImpactEffect = function(shipID, position, damage)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return end

        -- Main impact effect
        local effectData = EffectData()
        effectData:SetOrigin(position)
        effectData:SetMagnitude(damage)
        effectData:SetScale(1)
        effectData:SetEntity(shield.shipCore)
        util.Effect(ASC.Shields.Config.ShieldEffects.HitEffect, effectData)

        -- Play impact sound
        ASC.Shields.Core.PlayShieldSound(shipID, "hit")

        -- Create dynamic light
        local dlight = DynamicLight(shield.shipCore:EntIndex())
        if dlight then
            dlight.pos = position
            dlight.r = shield.shieldColor.r
            dlight.g = shield.shieldColor.g
            dlight.b = shield.shieldColor.b
            dlight.brightness = 2
            dlight.decay = 1000
            dlight.size = 100
            dlight.dietime = CurTime() + 0.3
        end
    end,

    -- Create recharge effect
    CreateRechargeEffect = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then return end

        local effectData = EffectData()
        effectData:SetOrigin(shield.shipCore:GetPos())
        effectData:SetEntity(shield.shipCore)
        effectData:SetScale(shield.shieldRadius / 100)
        util.Effect(ASC.Shields.Config.ShieldEffects.RechargeEffect, effectData)
    end,

    -- Create overload effect
    CreateOverloadEffect = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then return end

        local effectData = EffectData()
        effectData:SetOrigin(shield.shipCore:GetPos())
        effectData:SetEntity(shield.shipCore)
        effectData:SetMagnitude(3)
        effectData:SetScale(shield.shieldRadius / 50)
        util.Effect(ASC.Shields.Config.ShieldEffects.OverloadEffect, effectData)
    end,

    -- Play shield sound
    PlayShieldSound = function(shipID, soundType)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then return end

        local sounds = ASC.Shields.Config.ShieldEffects.Sounds
        local sound = sounds[soundType]

        if sound then
            shield.shipCore:EmitSound(sound, 75, 100)
        end
    end,
    
    -- Detect bonus shield generators (optional enhancement)
    DetectBonusGenerators = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then return end

        shield.generators = {}

        -- Find optional shield generators for bonus strength
        if shield.shipCore.GetEntities then
            for _, ent in ipairs(shield.shipCore:GetEntities()) do
                if IsValid(ent) and (ent:GetClass() == "asc_shield_generator" or ent:GetClass() == "hyperdrive_shield_generator") then
                    table.insert(shield.generators, {
                        entity = ent,
                        active = true,
                        bonusStrength = 300, -- Bonus strength added to built-in shields
                        bonusRecharge = 15,  -- Bonus recharge rate
                        efficiency = 1.0
                    })
                end
            end
        end

        if #shield.generators > 0 then
            print("[Shield System] Detected " .. #shield.generators .. " bonus shield generators for ship " .. shipID)
        end
    end,
    
    -- Detect CAP shields
    DetectCAPShields = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) or not ASC.Shields.Config.CAPIntegration then return end
        
        shield.capShields = {}
        
        -- Check for CAP shield integration
        if HYPERDRIVE and HYPERDRIVE.CAP and HYPERDRIVE.CAP.Shields then
            local capShields = HYPERDRIVE.CAP.Shields.FindShields(shield.shipCore.ship or {})
            for _, capShield in ipairs(capShields) do
                if IsValid(capShield) then
                    table.insert(shield.capShields, {
                        entity = capShield,
                        active = true,
                        strength = 500,
                        type = "CAP_BUBBLE"
                    })
                end
            end
        end
        
        print("[Shield System] Detected " .. #shield.capShields .. " CAP shields for ship " .. shipID)
    end,
    
    -- Activate built-in shields (no generators required)
    ActivateBuiltinShields = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return false end

        -- Start with base shield strength from ship core
        local baseStrength = shield.config.strength
        local totalRechargeRate = shield.config.rechargeRate

        -- Add bonus from optional generators
        local bonusStrength = 0
        local bonusRecharge = 0
        for _, generator in ipairs(shield.generators) do
            if IsValid(generator.entity) and generator.active then
                bonusStrength = bonusStrength + generator.bonusStrength
                bonusRecharge = bonusRecharge + generator.bonusRecharge
            end
        end

        -- Add CAP shield integration bonus
        local capBonus = 0
        for _, capShield in ipairs(shield.capShields) do
            if IsValid(capShield.entity) and capShield.active then
                capBonus = capBonus + capShield.strength
            end
        end

        -- Calculate final shield properties
        local totalStrength = baseStrength + bonusStrength + capBonus
        shield.maxStrength = totalStrength
        shield.currentStrength = totalStrength
        shield.config.rechargeRate = totalRechargeRate + bonusRecharge
        shield.active = true
        shield.recharging = false

        -- Update ship core with shield information
        if IsValid(shield.shipCore) then
            shield.shipCore:SetNWBool("ShieldSystemActive", true)
            shield.shipCore:SetNWFloat("ShieldStrength", 100)
            shield.shipCore:SetNWFloat("MaxShieldStrength", totalStrength)
            shield.shipCore:SetNWString("ShieldType", shield.shieldType)
            shield.shipCore:SetNWBool("BuiltinShields", true)
            shield.shipCore:SetNWInt("BonusGenerators", #shield.generators)
            shield.shipCore:SetNWInt("CAPShields", #shield.capShields)
        end

        -- Create initial shield effect
        ASC.Shields.Core.CreateShieldActivationEffect(shield.shipCore)

        -- Play activation sound
        if IsValid(shield.shipCore) then
            shield.shipCore:EmitSound(ASC.Shields.Config.ShieldEffects.ActivationSound, 60, 100)
        end

        local statusMsg = "Built-in: " .. baseStrength
        if bonusStrength > 0 then statusMsg = statusMsg .. " + Bonus: " .. bonusStrength end
        if capBonus > 0 then statusMsg = statusMsg .. " + CAP: " .. capBonus end

        print("[Shield System] Activated built-in shields for ship " .. shipID .. " - Total: " .. totalStrength .. " (" .. statusMsg .. ")")
        return true
    end,

    -- Create shield activation effect
    CreateShieldActivationEffect = function(shipCore)
        if not IsValid(shipCore) then return end

        local pos = shipCore:GetPos()

        -- Create activation effect
        local effect = EffectData()
        effect:SetOrigin(pos)
        effect:SetScale(5)
        effect:SetMagnitude(2)
        util.Effect("TeslaHitBoxes", effect)

        -- Create shield bubble effect
        timer.Simple(0.5, function()
            if IsValid(shipCore) then
                local bubble = EffectData()
                bubble:SetOrigin(pos)
                bubble:SetScale(3)
                bubble:SetRadius(150)
                util.Effect("ElectricSpark", bubble)
            end
        end)
    end,
    
    -- Update shield system
    Update = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not IsValid(shield.shipCore) then
            ASC.Shields.Core.ActiveShields[shipID] = nil
            return
        end
        
        local currentTime = CurTime()
        
        -- Handle recharging
        if shield.recharging and shield.currentStrength < shield.maxStrength then
            local rechargeAmount = shield.config.rechargeRate * ASC.Shields.Config.UpdateRate
            shield.currentStrength = math.min(shield.maxStrength, shield.currentStrength + rechargeAmount)
            
            -- Update ship core
            local strengthPercent = (shield.currentStrength / shield.maxStrength) * 100
            shield.shipCore:SetNWFloat("ShieldStrength", strengthPercent)
            
            -- Stop recharging when full
            if shield.currentStrength >= shield.maxStrength then
                shield.recharging = false
                shield.rechargeCount = shield.rechargeCount + 1
            end
        end
        
        -- Start recharging if enough time has passed since damage
        if not shield.recharging and shield.currentStrength < shield.maxStrength then
            if currentTime - shield.lastDamageTime >= ASC.Shields.Config.RechargeDelay then
                shield.recharging = true
            end
        end
        
        -- Update tactical AI integration
        if ASC.Shields.Config.TacticalAIIntegration then
            ASC.Shields.Core.UpdateTacticalIntegration(shipID)
        end
        
        -- Update adaptive shielding
        if ASC.Shields.Config.AdaptiveShielding then
            ASC.Shields.Core.UpdateAdaptiveShielding(shipID)
        end
    end,
    
    -- Handle shield damage
    AbsorbDamage = function(shipID, damage, damageType, attacker)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not shield.active or shield.currentStrength <= 0 then
            return 0 -- No damage absorbed
        end
        
        local absorbedDamage = math.min(damage, shield.currentStrength)
        shield.currentStrength = shield.currentStrength - absorbedDamage
        shield.lastDamageTime = CurTime()
        shield.recharging = false
        shield.damageAbsorbed = shield.damageAbsorbed + absorbedDamage
        
        -- Update ship core
        local strengthPercent = (shield.currentStrength / shield.maxStrength) * 100
        shield.shipCore:SetNWFloat("ShieldStrength", strengthPercent)
        
        -- Shield effects
        ASC.Shields.Core.CreateShieldHitEffect(shield.shipCore, attacker)
        
        -- Play hit sound
        if IsValid(shield.shipCore) then
            local hitSound = ASC.Shields.Config.ShieldEffects.HitSound .. math.random(1, 6) .. ".wav"
            shield.shipCore:EmitSound(hitSound, 70, math.random(90, 110))
        end
        
        -- Check for shield overload
        if shield.currentStrength <= 0 then
            ASC.Shields.Core.ShieldOverload(shipID)
        end
        
        print("[Shield System] Absorbed " .. absorbedDamage .. " damage - Remaining: " .. shield.currentStrength)
        return absorbedDamage
    end,
    
    -- Handle shield overload
    ShieldOverload = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return end
        
        shield.active = false
        shield.overloadCount = shield.overloadCount + 1
        
        -- Update ship core
        shield.shipCore:SetNWBool("ShieldSystemActive", false)
        shield.shipCore:SetNWFloat("ShieldStrength", 0)
        
        -- Overload effects
        if IsValid(shield.shipCore) then
            shield.shipCore:EmitSound(ASC.Shields.Config.ShieldEffects.OverloadSound, 80, 80)
            
            local explosion = EffectData()
            explosion:SetOrigin(shield.shipCore:GetPos())
            explosion:SetScale(3)
            util.Effect(ASC.Shields.Config.ShieldEffects.OverloadEffect, explosion)
        end
        
        -- Restart built-in shields after delay
        timer.Simple(5, function()
            if IsValid(shield.shipCore) then
                ASC.Shields.Core.ActivateBuiltinShields(shipID)
            end
        end)
        
        print("[Shield System] Shield overload for ship " .. shipID)
    end,
    
    -- Create shield hit effect
    CreateShieldHitEffect = function(shipCore, attacker)
        if not IsValid(shipCore) then return end
        
        local hitPos = shipCore:GetPos()
        if IsValid(attacker) then
            hitPos = hitPos + (attacker:GetPos() - shipCore:GetPos()):GetNormalized() * 100
        end
        
        local effect = EffectData()
        effect:SetOrigin(hitPos)
        effect:SetStart(shipCore:GetPos())
        effect:SetScale(2)
        effect:SetMagnitude(1)
        util.Effect(ASC.Shields.Config.ShieldEffects.HitEffect, effect)
    end,
    
    -- Update tactical AI integration
    UpdateTacticalIntegration = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield or not ASC or not ASC.TacticalAI then return end
        
        local session = ASC.TacticalAI.Core.ActiveSessions[shipID]
        if session then
            local threatCount = table.Count(session.threats)
            shield.threatLevel = threatCount
            
            -- Adaptive shield strength based on threat level
            if threatCount > 3 then
                shield.adaptiveMode = true
                -- Boost recharge rate during high threat
                shield.config.rechargeRate = ASC.Shields.Config.ShieldTypes[shield.shieldType].rechargeRate * 1.5
            else
                shield.adaptiveMode = false
                shield.config.rechargeRate = ASC.Shields.Config.ShieldTypes[shield.shieldType].rechargeRate
            end
        end
    end,
    
    -- Update adaptive shielding
    UpdateAdaptiveShielding = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then return end
        
        -- Adjust efficiency based on performance
        local damageRatio = shield.damageAbsorbed / (shield.maxStrength * (shield.rechargeCount + 1))
        if damageRatio > 0.8 then
            shield.efficiency = math.min(1.2, shield.efficiency + 0.01) -- Learn to be more efficient
        elseif damageRatio < 0.3 then
            shield.efficiency = math.max(0.8, shield.efficiency - 0.005) -- Reduce efficiency if underused
        end
    end,
    
    -- Get shield status
    GetShieldStatus = function(shipID)
        local shield = ASC.Shields.Core.ActiveShields[shipID]
        if not shield then
            return {
                available = false,
                strengthPercent = 0,
                status = "OFFLINE"
            }
        end
        
        local strengthPercent = (shield.currentStrength / shield.maxStrength) * 100
        local status = "OFFLINE"
        
        if shield.active then
            if shield.recharging then
                status = "RECHARGING"
            elseif strengthPercent > 75 then
                status = "STRONG"
            elseif strengthPercent > 25 then
                status = "MODERATE"
            else
                status = "WEAK"
            end
        end
        
        return {
            available = shield.active,
            strengthPercent = strengthPercent,
            currentStrength = shield.currentStrength,
            maxStrength = shield.maxStrength,
            status = status,
            recharging = shield.recharging,
            efficiency = shield.efficiency,
            damageAbsorbed = shield.damageAbsorbed,
            generators = #shield.generators,
            capShields = #shield.capShields
        }
    end
}

-- Initialize system
if SERVER then
    -- Update all shield systems
    timer.Create("ASC_Shields_Update", ASC.Shields.Config.UpdateRate, 0, function()
        if ASC.Shields and ASC.Shields.Core and ASC.Shields.Core.ActiveShields then
            for shipID, shield in pairs(ASC.Shields.Core.ActiveShields) do
                if ASC.Shields.Core.Update then
                    ASC.Shields.Core.Update(shipID)
                end
            end
        end
    end)
    
    -- Update system status
    ASC.SystemStatus = ASC.SystemStatus or {}
    ASC.SystemStatus.ShieldSystem = true

    print("[Advanced Space Combat] Shield System v3.0.0 loaded")
end
