--[[
    Advanced Space Combat - AI Fighter Client
]]

include("shared.lua")

function ENT:Initialize()
    -- Client-side initialization
end

function ENT:Draw()
    self:DrawModel()
    
    -- Draw simple status indicator
    local pos = self:GetPos() + Vector(0, 0, 20)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang.Forward, 90)
    ang:RotateAroundAxis(ang.Right, 90)
    
    local distance = LocalPlayer():GetPos():Distance(pos)
    if distance > 500 then return end
    
    local scale = math.Clamp(1 - (distance / 500), 0.1, 1)
    
    cam.Start3D2D(pos, ang, scale * 0.1)
        draw.SimpleText("AI Fighter", "DermaDefault", 0, 0, Color(255, 100, 100), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    cam.End3D2D()
end
