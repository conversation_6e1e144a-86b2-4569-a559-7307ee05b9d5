"VertexLitGeneric"
{
	"$basetexture" "hyperdrive/shield_generator"
	"$bumpmap" "hyperdrive/shield_generator_normal"
	"$envmap" "env_cubemap"
	"$envmaptint" "[0.1 0.2 0.4]"
	"$envmapfresnel" "1"
	"$phong" "1"
	"$phongexponent" "30"
	"$phongboost" "1.2"
	"$phongtint" "[0.6 0.8 1.0]"
	"$rimlight" "1"
	"$rimlightexponent" "3"
	"$rimlightboost" "1.5"
	"$selfillum" "1"
	"$selfillummask" "hyperdrive/shield_generator_illum"
	"$selfillumtint" "[0.2 0.5 1.0]"
	
	"Proxies"
	{
		"Sine"
		{
			"sineperiod" "2"
			"sinemin" "0.3"
			"sinemax" "1.0"
			"resultvar" "$selfillumtint[2]"
		}
	}
}
