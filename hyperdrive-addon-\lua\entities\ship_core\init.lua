-- Ship Core Compatibility Entity - Server Side
-- Automatically replaces itself with ASC Ship Core

AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    self:SetModel("models/props_combine/combine_core.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(50)
    end
    
    -- Set a distinctive color to show this is redirecting
    self:SetColor(Color(100, 150, 255, 200))
    
    print("[Ship Core Legacy] Legacy ship core spawned, will redirect to ASC Ship Core")
    
    -- Automatically replace with ASC ship core after a short delay
    timer.Simple(1, function()
        if IsValid(self) then
            self:ReplaceWithASCShipCore()
        end
    end)
end

function ENT:ReplaceWithASCShipCore()
    if not IsValid(self) then return end
    
    local pos = self:GetPos()
    local ang = self:GetAngles()
    local owner = self:CPPIGetOwner() or self:GetOwner()
    
    print("[Ship Core Legacy] Replacing legacy ship core with ASC Ship Core at " .. tostring(pos))
    
    -- Create new ASC ship core
    local newCore = ents.Create("asc_ship_core")
    if not IsValid(newCore) then
        print("[Ship Core Legacy] Failed to create ASC ship core replacement")
        return
    end
    
    newCore:SetPos(pos)
    newCore:SetAngles(ang)
    newCore:Spawn()
    newCore:Activate()
    
    -- Set ownership
    if IsValid(owner) then
        if newCore.CPPISetOwner then
            newCore:CPPISetOwner(owner)
        elseif newCore.SetOwner then
            newCore:SetOwner(owner)
        end
        
        -- Notify owner
        if owner:IsPlayer() then
            owner:ChatPrint("[ASC] Legacy ship core automatically upgraded to ASC Ship Core")
        end
    end
    
    -- Copy any properties if needed
    if self.GetShipName and newCore.SetShipName then
        local shipName = self:GetShipName()
        if shipName and shipName ~= "" then
            newCore:SetShipName(shipName)
        end
    end
    
    print("[Ship Core Legacy] Successfully replaced legacy ship core with ASC Ship Core")
    
    -- Remove the legacy core
    self:Remove()
end

-- Compatibility functions for systems that might interact with this before replacement
function ENT:GetShipDetected()
    return false  -- Not detected until replaced
end

function ENT:GetCoreValid()
    return true  -- Valid for replacement
end

function ENT:GetShipType()
    return "Legacy Ship Core (Redirecting)"
end

function ENT:GetStatusMessage()
    return "Upgrading to ASC Ship Core..."
end

function ENT:GetShipName()
    return "Legacy Ship"
end

-- Prevent use until replaced
function ENT:Use(activator, caller)
    if IsValid(activator) and activator:IsPlayer() then
        activator:ChatPrint("[ASC] This legacy ship core is being upgraded to ASC Ship Core...")
    end
end

-- Handle tool interactions
function ENT:CanTool(ply, trace, mode)
    if IsValid(ply) and ply:IsPlayer() then
        ply:ChatPrint("[ASC] This legacy ship core is being upgraded. Please wait...")
    end
    return false  -- Block tools until replaced
end
