--[[
    Advanced Space Combat - Shield Bubble Client
    
    Client-side shield bubble rendering and effects
]]

include("shared.lua")

function ENT:Initialize()
    -- Client-side initialization
    self.ShieldRadius = 200
    self.ShieldStrength = 1000
    self.MaxShieldStrength = 1000
    self.ShieldColor = Color(100, 150, 255, 100)
    self.Active = true
    
    -- Visual effects
    self.PulseIntensity = 0
    self.DamageFlash = 0
    self.LastDamageTime = 0
    self.HitPositions = {}
    
    -- Particle system
    self.ParticleEmitter = nil
    self.LastParticleTime = 0
    
    -- Material
    self.ShieldMaterial = Material("sprites/bubble")
    
    -- Animation
    self.AnimationTime = 0
    self.WaveOffset = math.random() * 10
end

-- Draw shield bubble
function ENT:Draw()
    -- Don't draw the model
    -- self:DrawModel()
    
    if not self:GetNWBool("Active", true) then return end
    
    local radius = self:GetNWFloat("ShieldRadius", 200)
    local strength = self:GetNWFloat("ShieldStrength", 1000)
    local maxStrength = self:GetNWFloat("MaxShieldStrength", 1000)
    local colorVec = self:GetNWVector("ShieldColor", Vector(100, 150, 255))
    local alpha = self:GetNWFloat("ShieldAlpha", 100)
    local pulseIntensity = self:GetNWFloat("PulseIntensity", 0)
    local damageFlash = self:GetNWFloat("DamageFlash", 0)
    
    if strength <= 0 then return end
    
    -- Calculate shield color with pulse and damage flash
    local color = Color(colorVec.x, colorVec.y, colorVec.z, alpha)
    
    -- Apply pulse effect
    local pulseAlpha = alpha * (0.7 + pulseIntensity * 0.3)
    
    -- Apply damage flash
    if damageFlash > 0 then
        color.r = math.min(255, color.r + damageFlash * 100)
        pulseAlpha = pulseAlpha + damageFlash * 50
    end
    
    color.a = pulseAlpha
    
    -- Draw shield sphere
    self:DrawShieldSphere(radius, color)
    
    -- Draw hit effects
    self:DrawHitEffects()
    
    -- Update animation
    self.AnimationTime = self.AnimationTime + FrameTime()
end

-- Draw shield sphere
function ENT:DrawShieldSphere(radius, color)
    local pos = self:GetPos()
    local segments = 32
    local rings = 16
    
    -- Set render state
    render.SetMaterial(self.ShieldMaterial)
    render.SetBlend(color.a / 255)
    
    -- Draw sphere using triangles
    local vertices = {}
    
    for ring = 0, rings do
        local ringAngle = (ring / rings) * math.pi
        local ringRadius = math.sin(ringAngle) * radius
        local ringY = math.cos(ringAngle) * radius
        
        for seg = 0, segments do
            local segAngle = (seg / segments) * 2 * math.pi
            local x = math.cos(segAngle) * ringRadius
            local z = math.sin(segAngle) * ringRadius
            
            local vertex = {
                pos = pos + Vector(x, ringY, z),
                normal = Vector(x, ringY, z):GetNormalized(),
                u = seg / segments,
                v = ring / rings
            }
            
            table.insert(vertices, vertex)
        end
    end
    
    -- Draw triangles
    render.OverrideColorWriteEnable(true, true)
    render.OverrideAlphaWriteEnable(true)
    
    mesh.Begin(MATERIAL_TRIANGLES, #vertices / 3)
    
    for i = 1, #vertices, 3 do
        if vertices[i] and vertices[i+1] and vertices[i+2] then
            mesh.Position(vertices[i].pos)
            mesh.Normal(vertices[i].normal)
            mesh.TexCoord(0, vertices[i].u, vertices[i].v)
            mesh.Color(color.r, color.g, color.b, color.a)
            mesh.AdvanceVertex()
            
            mesh.Position(vertices[i+1].pos)
            mesh.Normal(vertices[i+1].normal)
            mesh.TexCoord(0, vertices[i+1].u, vertices[i+1].v)
            mesh.Color(color.r, color.g, color.b, color.a)
            mesh.AdvanceVertex()
            
            mesh.Position(vertices[i+2].pos)
            mesh.Normal(vertices[i+2].normal)
            mesh.TexCoord(0, vertices[i+2].u, vertices[i+2].v)
            mesh.Color(color.r, color.g, color.b, color.a)
            mesh.AdvanceVertex()
        end
    end
    
    mesh.End()
    
    render.OverrideColorWriteEnable(false)
    render.OverrideAlphaWriteEnable(false)
end

-- Draw hit effects
function ENT:DrawHitEffects()
    local currentTime = CurTime()
    
    -- Draw ripple effects at hit positions
    for i, hit in ipairs(self.HitPositions) do
        local age = currentTime - hit.time
        if age < 2.0 then
            local alpha = (1 - age / 2.0) * 255
            local rippleRadius = age * 100
            
            -- Draw expanding ripple
            self:DrawRipple(hit.pos, rippleRadius, Color(255, 255, 255, alpha))
        end
    end
end

-- Draw ripple effect
function ENT:DrawRipple(center, radius, color)
    local segments = 24
    
    render.SetMaterial(Material("sprites/light_glow02_add"))
    
    mesh.Begin(MATERIAL_TRIANGLES, segments * 2)
    
    for i = 0, segments - 1 do
        local angle1 = (i / segments) * 2 * math.pi
        local angle2 = ((i + 1) / segments) * 2 * math.pi
        
        local pos1 = center + Vector(math.cos(angle1) * radius, 0, math.sin(angle1) * radius)
        local pos2 = center + Vector(math.cos(angle2) * radius, 0, math.sin(angle2) * radius)
        
        -- Inner triangle
        mesh.Position(center)
        mesh.Color(color.r, color.g, color.b, color.a)
        mesh.TexCoord(0, 0.5, 0.5)
        mesh.AdvanceVertex()
        
        mesh.Position(pos1)
        mesh.Color(color.r, color.g, color.b, 0)
        mesh.TexCoord(0, 0, 0)
        mesh.AdvanceVertex()
        
        mesh.Position(pos2)
        mesh.Color(color.r, color.g, color.b, 0)
        mesh.TexCoord(0, 1, 0)
        mesh.AdvanceVertex()
    end
    
    mesh.End()
end

-- Think function for client effects
function ENT:Think()
    local currentTime = CurTime()
    
    -- Update particle effects
    if currentTime - self.LastParticleTime > 0.1 then
        self:UpdateParticleEffects()
        self.LastParticleTime = currentTime
    end
    
    -- Update hit positions from networking
    self:UpdateHitPositions()
end

-- Update particle effects
function ENT:UpdateParticleEffects()
    if not self:GetNWBool("Active", true) then return end
    
    local strength = self:GetNWFloat("ShieldStrength", 1000)
    local maxStrength = self:GetNWFloat("MaxShieldStrength", 1000)
    local radius = self:GetNWFloat("ShieldRadius", 200)
    
    if strength <= 0 then return end
    
    -- Create particle emitter if needed
    if not self.ParticleEmitter then
        self.ParticleEmitter = ParticleEmitter(self:GetPos())
    end
    
    -- Emit ambient particles
    local strengthPercent = strength / maxStrength
    local particleCount = math.ceil(strengthPercent * 3)
    
    for i = 1, particleCount do
        if math.random() < 0.3 then
            local particle = self.ParticleEmitter:Add("effects/spark", self:GetPos() + VectorRand() * radius * 0.9)
            if particle then
                particle:SetVelocity(VectorRand() * 20)
                particle:SetLifeTime(0)
                particle:SetDieTime(1 + math.random() * 2)
                particle:SetStartAlpha(100 * strengthPercent)
                particle:SetEndAlpha(0)
                particle:SetStartSize(2)
                particle:SetEndSize(0)
                
                local colorVec = self:GetNWVector("ShieldColor", Vector(100, 150, 255))
                particle:SetColor(colorVec.x, colorVec.y, colorVec.z)
            end
        end
    end
end

-- Update hit positions
function ENT:UpdateHitPositions()
    local lastDamageTime = self:GetNWFloat("LastDamageTime", 0)
    
    if lastDamageTime > self.LastDamageTime then
        -- New damage detected, add visual effect
        self.LastDamageTime = lastDamageTime
        
        -- Create screen shake for nearby players
        local localPlayer = LocalPlayer()
        if IsValid(localPlayer) then
            local distance = localPlayer:GetPos():Distance(self:GetPos())
            if distance < 1000 then
                local intensity = math.Clamp(1 - (distance / 1000), 0, 1)
                localPlayer:ViewPunch(Angle(math.random(-1, 1) * intensity, math.random(-1, 1) * intensity, 0))
            end
        end
    end
end

-- Cleanup
function ENT:OnRemove()
    if self.ParticleEmitter then
        self.ParticleEmitter:Finish()
        self.ParticleEmitter = nil
    end
end
