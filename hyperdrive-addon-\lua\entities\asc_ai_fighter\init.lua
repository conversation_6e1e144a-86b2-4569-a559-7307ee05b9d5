--[[
    Advanced Space Combat - AI Fighter
    
    Small AI-controlled fighter ship spawned by boss ships
]]

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    -- Set model
    self:SetModel("models/props_phx/construct/metal_plate1x1.mdl")
    
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(25)
        phys:EnableGravity(false)
    end
    
    -- Fighter properties
    self:SetMaxHealth(500)
    self:SetHealth(500)
    
    -- AI properties
    self.Target = nil
    self.Speed = 200
    self.Agility = 1.2
    self.WeaponRange = 800
    self.LastFired = 0
    self.FireRate = 0.5
    self.Damage = 25
    
    -- Behavior
    self.LastUpdate = CurTime()
    self.UpdateRate = 0.1
    self.Lifetime = 120 -- 2 minutes
    
    -- Remove after lifetime
    timer.Simple(self.Lifetime, function()
        if IsValid(self) then
            self:Explode()
        end
    end)
    
    print("[ASC AI Fighter] Fighter spawned")
end

function ENT:Think()
    local currentTime = CurTime()
    
    if currentTime - self.LastUpdate < self.UpdateRate then
        return
    end
    
    self.LastUpdate = currentTime
    
    -- Find target if we don't have one
    if not self.Target or not IsValid(self.Target) then
        self:FindTarget()
    end
    
    -- AI behavior
    if self.Target and IsValid(self.Target) then
        self:AttackTarget()
    else
        self:Patrol()
    end
    
    self:NextThink(currentTime + self.UpdateRate)
    return true
end

function ENT:FindTarget()
    local myPos = self:GetPos()
    local closestTarget = nil
    local closestDistance = math.huge
    
    -- Look for players
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local distance = myPos:Distance(ply:GetPos())
            if distance < 1500 and distance < closestDistance then
                closestTarget = ply
                closestDistance = distance
            end
        end
    end
    
    -- Look for ship cores
    for _, ent in ipairs(ents.FindByClass("asc_ship_core")) do
        if IsValid(ent) then
            local distance = myPos:Distance(ent:GetPos())
            if distance < 1500 and distance < closestDistance then
                closestTarget = ent
                closestDistance = distance
            end
        end
    end
    
    self.Target = closestTarget
end

function ENT:AttackTarget()
    if not self.Target or not IsValid(self.Target) then
        return
    end
    
    local myPos = self:GetPos()
    local targetPos = self.Target:GetPos()
    local distance = myPos:Distance(targetPos)
    
    -- Move towards target
    if distance > self.WeaponRange * 0.8 then
        self:MoveTowards(targetPos)
    else
        -- Strafe around target
        self:StrafeTarget()
    end
    
    -- Fire weapons
    if distance <= self.WeaponRange and CurTime() - self.LastFired > (1 / self.FireRate) then
        self:FireWeapon()
        self.LastFired = CurTime()
    end
end

function ENT:Patrol()
    -- Simple patrol behavior
    local myPos = self:GetPos()
    local patrolTarget = myPos + VectorRand() * 300
    self:MoveTowards(patrolTarget)
end

function ENT:MoveTowards(targetPos)
    local myPos = self:GetPos()
    local direction = (targetPos - myPos):GetNormalized()
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:SetVelocity(direction * self.Speed)
        self:SetAngles(direction:Angle())
    end
end

function ENT:StrafeTarget()
    if not self.Target then return end
    
    local myPos = self:GetPos()
    local targetPos = self.Target:GetPos()
    local direction = (targetPos - myPos):GetNormalized()
    local rightVector = direction:Cross(Vector(0, 0, 1)):GetNormalized()
    
    -- Strafe left or right
    local strafeDirection = (CurTime() % 6 < 3) and 1 or -1
    local strafeTarget = myPos + rightVector * strafeDirection * 200
    
    self:MoveTowards(strafeTarget)
end

function ENT:FireWeapon()
    if not self.Target then return end
    
    local startPos = self:GetPos()
    local targetPos = self.Target:GetPos()
    
    -- Create projectile effect
    local effectData = EffectData()
    effectData:SetStart(startPos)
    effectData:SetOrigin(targetPos)
    effectData:SetEntity(self)
    util.Effect("asc_fighter_beam", effectData)
    
    -- Apply damage
    local dmgInfo = DamageInfo()
    dmgInfo:SetDamage(self.Damage)
    dmgInfo:SetAttacker(self)
    dmgInfo:SetInflictor(self)
    dmgInfo:SetDamageType(DMG_ENERGYBEAM)
    
    self.Target:TakeDamageInfo(dmgInfo)
    
    -- Sound effect
    self:EmitSound("weapons/physcannon/energy_sing_loop4.wav", 60, 120)
end

function ENT:SetTarget(target)
    self.Target = target
end

function ENT:OnTakeDamage(dmginfo)
    self:SetHealth(self:Health() - dmginfo:GetDamage())
    
    if self:Health() <= 0 then
        self:Explode()
    end
end

function ENT:Explode()
    local effectdata = EffectData()
    effectdata:SetOrigin(self:GetPos())
    effectdata:SetMagnitude(1)
    util.Effect("Explosion", effectdata)
    
    self:EmitSound("ambient/explosions/explode_4.wav", 80, 100)
    self:Remove()
end
