--[[
    Advanced Space Combat - Base Weapon Shared
]]

ENT.Type = "anim"
ENT.Base = "base_gmodentity"

ENT.PrintName = "ASC Base Weapon"
ENT.Author = "Advanced Space Combat"
ENT.Contact = ""
ENT.Purpose = "Base class for all ASC weapons"
ENT.Instructions = "Use to toggle auto-fire mode"

ENT.Spawnable = false
ENT.AdminSpawnable = false
ENT.Category = "Advanced Space Combat - Weapons"

-- Default weapon properties (can be overridden by child classes)
ENT.WeaponName = "Base Weapon"
ENT.WeaponType = "energy"
ENT.WeaponModel = "models/props_combine/combine_interface001.mdl"
ENT.WeaponMass = 100

-- Combat properties
ENT.Damage = 100
ENT.Range = 2000
ENT.FireRate = 1.0
ENT.EnergyConsumption = 25
ENT.ProjectileSpeed = 1000
ENT.ProjectileType = "energy_bolt"
ENT.Accuracy = 0.9

-- Heat management
ENT.MaxHeat = 100
ENT.HeatPerShot = 10
ENT.CooldownRate = 20
ENT.OverheatThreshold = 90

-- Ammunition
ENT.MaxAmmo = -1 -- -1 = unlimited
ENT.AmmoType = "energy"
ENT.AmmoRegenRate = 0

-- Sounds
ENT.Sounds = {
    fire = "weapons/physcannon/energy_sing_loop4.wav",
    overheat = "ambient/energy/spark6.wav",
    cooldown = "ambient/energy/weld2.wav",
    charge = "ambient/energy/weld1.wav"
}

-- Network variables
function ENT:SetupDataTables()
    self:NetworkVar("Bool", 0, "Active")
    self:NetworkVar("Bool", 1, "AutoFire")
    self:NetworkVar("Bool", 2, "Overheated")
    self:NetworkVar("Float", 0, "Heat")
    self:NetworkVar("Float", 1, "Ammo")
    self:NetworkVar("String", 0, "WeaponName")
    self:NetworkVar("String", 1, "WeaponType")
    self:NetworkVar("Entity", 0, "Target")
end
